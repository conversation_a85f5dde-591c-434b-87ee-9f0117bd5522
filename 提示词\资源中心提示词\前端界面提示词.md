### 合作伙伴

@前端页面自动生成提示词模板.md 现在生成资源中心的相关界面。实现过程请遵守前端页面自动生成提示词模板.md文件中的规范。资源中心首页的路径src\views\infra\ResourceCenter\index.vue 请按照附件截图完成首页界面的设计

现在请参照附件截图完成新增合作伙伴界面的设计。新增合作伙伴页面路径：src\views\infra\ResourceCenter\components\AddPartner.vue。请注意这几个截图都属于新增合作伙伴界面的内容

在资源中心首页右上角添加了“新增资源”按钮，按钮位于Tabs和统计卡片上方。点击“新增资源”按钮，会从右侧滑出 AddPartner.vue 抽屉表单，实现新增合作伙伴的完整流程。按钮和整体布局美观，交互流畅

@AddPartner.vue 每个模块添加一个浅色的背景颜色 模块与模块之前区分，机构基本信息、合作与商业信息、资质与结算信息、开票信息

@index.vue 点击操作栏 编辑按钮时也需要从右侧滑出抽屉AddPartner.vue；点击操作栏删除按钮时，暂时不请求后端接口，直接删除当前行提示删除成功

@OptLog.vue 这个是操作日志界面，请先参考附件截图完成界面设计，然后点击操作栏的“操作日志”按钮时，从右侧滑出 AddPartner.vue 抽屉

### 统一师资库

@前端页面自动生成提示词模板.md 现在生成统一师资库的相关界面。实现过程请遵守前端页面自动生成提示词模板.md文件中的规范。师资库的页面路径src\views\infra\ResourceCenter\TeacherDatabase\TeacherDatabase.vue 请按照附件截图完成界面的设计

将 index.vue 和 TeacherDatabase.vue 顶部4个统计卡片（stat-card）的高度整体缩小约1/3，使其更紧凑，减少页面占用空间，提升界面美观度。

将“资质类型”下拉选项改为固定值，具体内容如下（参考截图）：全部技能等级证书从业资格证书专业技能证书职业资格证学历证书培训证书获奖证书其他mock数据中的 qualificationType 字段改为上述真实类型字符串

@index.vue 右上角的新增按钮根据当前Tab动态切换名称和显示/隐藏，具体规则如下：合作伙伴：显示“新增合作伙伴”统一师资库：显示“新增师资”数字资产(课程)：显示“新增课程”场地资源管理：显示“新增场地”证书模板：显示“新建模板”考题管理：显示"新增考题"

@前端页面自动生成提示词模板.md 现在生成相关界面。实现过程请遵守前端页面自动生成提示词模板.md文件中的规范。页面路径src\views\infra\ResourceCenter\TeacherDatabase\components\AddTeacher.vue 请按照附件截图完成界面的设计


### 数字资产(课程)

@前端页面自动生成提示词模板.md 现在生成相关界面。实现过程请遵守前端页面自动生成提示词模板.md文件中的规范。页面路径src\views\infra\ResourceCenter\DigitalAsset\DigitalAsset.vue 请按照附件截图完成界面的设计

点击tab页切换的界面展示对照关系如下：合作伙伴：src\views\infra\ResourceCenter\index.vue统一师资库：src\views\infra\ResourceCenter\TeacherDatabase\TeacherDatabase.vue数字资产(课程)：src\views\infra\ResourceCenter\DigitalAsset\DigitalAsset.vue考题管理：src\views\infra\ResourceCenter\QuestionManagement\QuestionManagement.vue场地资源管理：src\views\infra\ResourceCenter\SiteManagement\SiteManagement.vue证书模板：src\views\infra\ResourceCenter\CertificateTemplate\CertificateTemplate.vue



现在请根据附件截图生成管理课程界面，页面路径：src\views\infra\ResourceCenter\DigitalAsset\components\ManagementCourse.vue
点击数字资产(课程)数据列表操作栏 课程管理 按钮，页面跳转到ManagementCourse.vue,不是弹窗的方式打开，就在当前页面直接跳转展示


@DigitalAsset.vue 点击数据列表操作栏的管理课程按钮需要调整一下，目前是点击后打开了一个新的管理课程路由，但实际需要的效果是在当前数据资产的tab页下面直接显示管理课程界面的内容，具体实现可参考考题管理下的点击分类管理按钮功能。
管理课程页面地址：src\views\infra\ResourceCenter\DigitalAsset\components\ManagementCourse.vue
考题管理页面地址：src\views\infra\ResourceCenter\QuestionManagement\QuestionManagement.vue
分类管理页面地址：src\views\infra\ResourceCenter\QuestionManagement\components\SortManagement.vue



### 考题管理

@前端页面自动生成提示词模板.md 现在生成相关界面。实现过程请遵守前端页面自动生成提示词模板.md文件中的规范。页面路径src\views\infra\ResourceCenter\QuestionManagement\QuestionManagement.vue 请按照附件截图完成界面的设计

分类管理、下载模板、导入考题按钮 放在筛选按钮后面，缩小按钮之间的间距，不要换行展示按钮。

@前端页面自动生成提示词模板.md 现在生成相关界面。实现过程请遵守前端页面自动生成提示词模板.md文件中的规范。页面路径src\views\infra\ResourceCenter\QuestionManagement\components\AddQuestion.vue请按照附件截图完成界面的设计


@QuestionManagement.vue 点击数据列操作栏的删除按钮时 添加删除事件，暂时不请求后端接口，直接将当前行删除并提示删除成功

@QuestionManagement.vue 操作列按钮调整成：编辑、复制、操作日志、删除，不需要详情按钮

@QuestionManagement.vue 点击操作列复制按钮时，从右侧滑出抽屉AddQuestion.vue，并将当前行的内容带入AddQuestion.vue界面的表单内容

@前端页面自动生成提示词模板.md 现在生成相关界面。实现过程请遵守前端页面自动生成提示词模板.md文件中的规范。页面路径src\views\infra\ResourceCenter\QuestionManagement\components\QuestionOptLog.vue请按照附件截图完成界面的设计


@QuestionManagement.vue 点击操作列操作日志按钮时，从右侧滑出抽屉QuestionOptLog.vue



@前端页面自动生成提示词模板.md 现在生成相关界面。实现过程请遵守前端页面自动生成提示词模板.md文件中的规范。页面路径src\views\infra\ResourceCenter\QuestionManagement\components\SortManagement.vue请按照附件截图完成界面的设计


考题管理搜索区域中将业务模块后面的内容 包括筛选、分类管理 下载模板、导入考题按钮都换行展示


样式越调越乱了，现在按照我的步骤来执行
1. 一级名称、二级名称、三级名称按照现有样式不动
2. 将题型下拉框选择移动到三级名称后面，如果div宽度不够就调整宽度，不要让题型换行展示


### 场地资源管理

@前端页面自动生成提示词模板.md 现在生成相关界面。实现过程请遵守前端页面自动生成提示词模板.md文件中的规范。页面路径src\views\infra\ResourceCenter\SiteManagement\SiteManagement.vue请按照附件截图完成界面的设计


顶部4个卡片的样式请参考合作伙伴的卡片样式进行调整

@前端页面自动生成提示词模板.md 现在生成相关界面。实现过程请遵守前端页面自动生成提示词模板.md文件中的规范
请按照附件截图完成界面的设计
场地操作日志页面路径：src\views\infra\ResourceCenter\SiteManagement\components\SiteOptLog.vue


@前端页面自动生成提示词模板.md 现在生成相关界面。实现过程请遵守前端页面自动生成提示词模板.md文件中的规范
请按照附件截图完成界面的设计
新增场地页面路径：src\views\infra\ResourceCenter\SiteManagement\components\AddSite.vue



@前端页面自动生成提示词模板.md 现在生成相关界面。实现过程请遵守前端页面自动生成提示词模板.md文件中的规范
请按照附件截图完成界面的设计
预约场地页面路径：src\views\infra\ResourceCenter\SiteManagement\components\SiteAppointment.vue


数据表格操作栏 按钮包括：编辑、预约、操作日志、删除,按钮功能如下：
编辑：从右侧滑出抽屉界面AddSite.vue
预约：弹窗展示 预约界面，路径：src\views\infra\ResourceCenter\SiteManagement\components\SiteAppointment.vue
操作日志：点击此按钮从右侧滑出抽屉 SiteOptLog.vue
删除：暂时没有后端接口，点击删除直接删除当前行并提示删除成功




@SchedulingBoard.vue 现在请完成页面的交互效果：
1. 针对界面的   el-date-picker 控件 v-model="currentDate"的操作，默认展示当前年月
2. 点击上月：控件 v-model="currentDate"切换到 已选择的上一个月份
3.点击下月，控件 v-model="currentDate"切换到 已选择的下一个月份
4. 点击 回到今日,控件 v-model="currentDate"换到 当前年月
5. 点击 周视图时，需要根据控件 v-model="currentDate" 选择的月份展示第一周到最后一周的场地预约情况，按周展示不需要按天展示


### 证书模板


@前端页面自动生成提示词模板.md 现在生成相关界面。实现过程请遵守前端页面自动生成提示词模板.md文件中的规范
请按照附件截图完成界面的设计
证书模板页面路径：
src\views\infra\ResourceCenter\CertificateTemplate\CertificateTemplate.vue
1.请注意界面自适应设计
2.顶部4个小卡片的样式参考合作伙伴的界面
3.新建模板按钮 紧凑排列到筛选按钮后面展示

@前端页面自动生成提示词模板.md 现在生成相关界面。实现过程请遵守前端页面自动生成提示词模板.md文件中的规范
请按照附件截图完成界面的设计
新增证书模板页面路径：
src\views\infra\ResourceCenter\CertificateTemplate\components\AddCertificateTemplate.vue

@index.vue 点击“新建模板”按钮，在“证书模板”Tab下会从右侧滑出抽屉式的 AddCertificateTemplate.vue 组件，实现新建证书模板的完整流程。

@前端页面自动生成提示词模板.md 现在生成相关界面。实现过程请遵守前端页面自动生成提示词模板.md文件中的规范
请按照附件截图完成界面的设计
预览证书页面路径：
src\views\infra\ResourceCenter\CertificateTemplate\components\PreviewCertificate.vue