<!--
  页面名称：商机中心首页
  功能描述：展示商机列表，支持多条件搜索、分页、编辑、跟进、删除等操作
-->
<template>
  <div class="business-center">
    <!-- 顶部搜索栏 -->
    <el-form :inline="true" :model="searchForm" class="search-form" @submit.prevent>
      <el-form-item label="业务模块：">
        <el-select
          v-model="searchForm.businessType"
          placeholder="全部"
          clearable
          style="width: 120px"
        >
          <el-option label="全部" :value="''" />
          <el-option
            v-for="item in businessTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="销售阶段：">
        <el-select
          v-model="searchForm.businessStage"
          placeholder="全部"
          clearable
          style="width: 120px"
        >
          <el-option label="全部" :value="''" />
          <el-option
            v-for="item in businessStageOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="销售负责人：">
        <el-select
          v-model="searchForm.ownerUserId"
          placeholder="全部"
          clearable
          style="width: 120px"
        >
          <el-option label="全部" :value="''" />
          <el-option
            v-for="user in userOptions"
            :key="user.id"
            :label="user.nickname || user.username"
            :value="user.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="searchForm.name"
          placeholder="搜索商机名称..."
          clearable
          style="width: 180px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">筛选</el-button>
        <el-button @click="onReset">重置</el-button>
      </el-form-item>
      <el-form-item style="float: right; margin-left: auto">
        <el-button type="primary" @click="onCreate" plain>+ 新建商机</el-button>
      </el-form-item>
    </el-form>

    <!-- 商机列表表格 -->
    <el-table :data="tableData" style="width: 100%; margin-top: 16px" border>
      <el-table-column prop="id" label="商机编号" width="120" />
      <el-table-column prop="name" label="商机名称" min-width="180" />
      <el-table-column prop="customerName" label="关联客户" min-width="120" />
      <el-table-column prop="businessTypeName" label="业务模块" min-width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.businessTypeName === '高校业务'" type="info">高校业务</el-tag>
          <el-tag v-else-if="scope.row.businessTypeName === '培训业务'" type="warning"
            >培训业务</el-tag
          >
          <el-tag v-else-if="scope.row.businessTypeName === '认证业务'" type="success"
            >认证业务</el-tag
          >
          <el-tag v-else>{{ scope.row.businessTypeName }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="totalPrice" label="商机金额(元)" min-width="100">
        <template #default="scope">
          {{ formatMoney(scope.row.totalPrice) }}
        </template>
      </el-table-column>
      <el-table-column prop="businessStageName" label="销售阶段" min-width="100" />
      <el-table-column prop="ownerUserName" label="销售负责人" min-width="100" />
      <el-table-column prop="createTime" label="创建时间" min-width="100">
        <template #default="scope">
          {{ formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="220">
        <template #default="scope">
          <el-button size="small" @click="onEdit(scope.row)">编辑</el-button>
          <el-button size="small" @click="onFollow(scope.row)">跟进</el-button>
          <el-button size="small" type="danger" @click="onDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pagination.page"
      v-model:page-size="pagination.pageSize"
      :total="pagination.total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      style="margin-top: 16px; text-align: right"
      @current-change="fetchList"
      @size-change="fetchList"
    />
    <AddBusiness ref="addBusinessRef" @close="onAddBusinessClose" />
    <FollowUpBusiness ref="followUpBusinessRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { getSimpleUserList } from '@/api/system/user'
import { getDictDataPage } from '@/api/system/dict/dict.data'
import request from '@/config/axios'
import AddBusiness from './components/AddBusiness.vue'
import FollowUpBusiness from './components/FollowUpBusiness.vue'

/** 搜索表单数据 */
const searchForm = ref({
  businessType: '',
  businessStage: '',
  ownerUserId: '',
  name: ''
})

/** 表格数据 */
const tableData = ref([])
/** 分页信息 */
const pagination = ref({ page: 1, pageSize: 10, total: 0 })

/** 下拉选项 */
const businessTypeOptions = ref<any[]>([])
const businessStageOptions = ref<any[]>([])
const userOptions = ref<any[]>([])

/** 获取业务模块、销售阶段、负责人下拉选项 */
const fetchOptions = async () => {
  // 业务模块
  const businessModuleRes = await getDictDataPage({
    dictType: 'business_module',
    pageNo: 1,
    pageSize: 100
  } as any)
  businessTypeOptions.value = (businessModuleRes?.list || []).map((item) => ({
    label: item.label,
    value: item.value
  }))
  // 销售阶段
  const salesStageRes = await getDictDataPage({
    dictType: 'business_sales_stage',
    pageNo: 1,
    pageSize: 100
  } as any)
  businessStageOptions.value = (salesStageRes?.list || []).map((item) => ({
    label: item.label,
    value: item.value
  }))
  // 负责人
  const userRes = await getSimpleUserList()
  userOptions.value = userRes
}

/** 获取商机列表 */
const fetchList = async () => {
  const params = {
    pageNo: pagination.value.page,
    pageSize: pagination.value.pageSize,
    businessType: searchForm.value.businessType,
    businessStage: searchForm.value.businessStage,
    ownerUserId: searchForm.value.ownerUserId,
    name: searchForm.value.name
  }
  const res = await request.get({ url: '/publicbiz/business/list', params })
  tableData.value = (res?.list || []).map((item) => ({
    ...item,
    businessTypeName: item.businessType, // 业务模块
    businessStageName: item.businessStage // 销售阶段
  }))
  pagination.value.total = res?.total || 0
}

/** 搜索 */
const onSearch = () => {
  pagination.value.page = 1
  fetchList()
}
/** 重置 */
const onReset = () => {
  searchForm.value = { businessType: '', businessStage: '', ownerUserId: '', name: '' }
  pagination.value.page = 1
  fetchList()
}

/** 编辑 */
const addBusinessRef = ref()
const onEdit = (row: any) => {
  addBusinessRef.value && addBusinessRef.value.open(row)
}
const followUpBusinessRef = ref()
/** 跟进 */
const onFollow = (row: any) => {
  followUpBusinessRef.value && followUpBusinessRef.value.open(row)
}
/** 删除 */
const onDelete = (row: any) => {
  ElMessageBox.confirm('确定要删除该商机吗？', '提示', { type: 'warning' })
    .then(async () => {
      await request.post({ url: '/publicbiz/business/delete', params: { id: row.id } })
      ElMessage.success('删除成功')
      await fetchList()
    })
    .catch(() => {})
}

/** 新建商机 */
const onCreate = () => {
  addBusinessRef.value && addBusinessRef.value.open()
}

/** 金额格式化 */
const formatMoney = (val: number) => {
  if (!val) return '0'
  return val.toLocaleString('zh-CN')
}
/** 日期格式化 */
const formatDate = (val: string | Date) => {
  if (!val) return ''
  const d = new Date(val)
  return d.toISOString().split('T')[0]
}

const onAddBusinessClose = () => {
  fetchList()
}

onMounted(async () => {
  await fetchOptions()
  await fetchList()
})
</script>

<style scoped lang="scss">
.business-center {
  padding: 24px;
  background: #fff;
  min-height: 100vh;
}
.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 8px;
}
</style>
