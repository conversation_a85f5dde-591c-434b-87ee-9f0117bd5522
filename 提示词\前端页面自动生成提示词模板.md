# 前端页面自动生成提示词模板

本模板适用于本项目所有前端页面的自动生成与开发规范，涵盖列表页、表单页、详情页三大常用页面类型。请团队成员或AI严格按照本模板生成和开发页面，确保代码风格统一、易于维护。

---

## 1. 列表页（如：用户列表、订单列表）

### 目录与命名规范

- 路径：`src/views/模块名/xxx.vue`
- 组件名、文件名：大驼峰，如 `UserList.vue`
- ts后缀的文件名：小写+中划线，如 `user-list.ts`

### 代码结构与内容要求

- 顶部注释：页面名称、功能描述
- 使用 `<script setup lang="ts">`
- 使用 `element-plus` 的 `el-table`、`el-pagination`
- 支持分页、搜索、排序、批量操作
- 数据接口调用放在 `src/api/模块名/xxx.ts`
- 响应式数据、方法、生命周期
- 代码注释齐全

### 常用交互

- 搜索栏（表单）
- 表格展示
- 分页
- 操作列（编辑、删除、详情等）
- 批量操作（可选）

### AI提示词模板

```
你有20年前端开发经验的工程师，请生成一个【列表页】代码，要求如下：
1. 页面功能：如"展示用户列表，支持搜索、分页、编辑、删除"
2. 路径：src/views/模块名/xxx.vue
3. 使用Vue3 + TypeScript + <script setup lang="ts">
4. 使用element-plus的el-table、el-pagination
5. 搜索栏用el-form，表格用el-table，分页用el-pagination
6. 数据接口调用src/api/模块名/下的接口
7. 代码需有详细注释，复杂逻辑需块注释
8. 页面顶部有功能描述注释
9. 支持操作列（编辑、删除、详情）
10. 响应式数据、方法、生命周期齐全
```

### 示例代码结构

```vue
<!--
  页面名称：用户列表
  功能描述：展示用户列表，支持搜索、分页、编辑、删除
-->
<template>
  <el-form :inline="true" @submit.prevent="onSearch">
    <el-form-item label="用户名">
      <el-input v-model="searchForm.username" placeholder="请输入用户名" />
    </el-form-item>
    <el-button type="primary" @click="onSearch">搜索</el-button>
  </el-form>
  <el-table :data="tableData" style="width: 100%">
    <el-table-column prop="username" label="用户名" />
    <el-table-column prop="email" label="邮箱" />
    <el-table-column label="操作">
      <template #default="scope">
        <el-button @click="onEdit(scope.row)">编辑</el-button>
        <el-button @click="onDelete(scope.row)" type="danger">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
  <el-pagination
    v-model:current-page="pagination.page"
    v-model:page-size="pagination.size"
    :total="pagination.total"
    @current-change="fetchList"
  />
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getUserList } from '@/api/user'

/** 搜索表单数据 */
const searchForm = ref({ username: '' })
/** 表格数据 */
const tableData = ref([])
/** 分页信息 */
const pagination = ref({ page: 1, size: 10, total: 0 })

/** 获取用户列表 */
const fetchList = async () => {
  // ...接口调用
}

const onSearch = () => {
  pagination.value.page = 1
  fetchList()
}

const onEdit = (row) => {
  /* ... */
}
const onDelete = (row) => {
  /* ... */
}

onMounted(fetchList)
</script>

<style scoped lang="scss">
// 页面样式
</style>
```

---

## 2. 表单页（如：新增/编辑用户、订单）

### 目录与命名规范

- 路径：`src/views/模块名/xxx.vue`
- 组件名、vue页面：大驼峰，如 `UserForm.vue`
- ts后缀的文件名：小写+中划线，如 `user-form.ts`

### 代码结构与内容要求

- 顶部注释：页面名称、功能描述
- 使用 `<script setup lang="ts">`
- 使用 `element-plus` 的 `el-form`、`el-form-item`、`el-input`、`el-select` 等
- 支持表单校验、重置、提交、回显（编辑时）
- 数据接口调用放在 `src/api/模块名/xxx.ts`
- 响应式数据、方法、生命周期
- 代码注释齐全

### 常用交互

- 表单输入、选择、校验
- 提交、重置
- 编辑时回显数据
- 成功/失败提示

### AI提示词模板

```
你是拥有20年前端开发经验的工程师，请生成一个【表单页】代码，要求如下：
1. 页面功能：如"新增/编辑用户，支持表单校验、提交、重置、编辑回显"
2. 路径：src/views/模块名/xxx.vue
3. 使用Vue3 + TypeScript + <script setup lang="ts">
4. 使用element-plus的el-form、el-form-item、el-input、el-select等
5. 表单需有校验规则，支持重置、提交
6. 编辑时自动回显数据
7. 数据接口调用src/api/模块名/下的接口
8. 代码需有详细注释，复杂逻辑需块注释
9. 页面顶部有功能描述注释
```

### 示例代码结构

```vue
<!--
  页面名称：用户表单
  功能描述：新增/编辑用户，支持表单校验、提交、重置、编辑回显
-->
<template>
  <el-form :model="form" :rules="rules" ref="formRef" label-width="80px">
    <el-form-item label="用户名" prop="username">
      <el-input v-model="form.username" />
    </el-form-item>
    <el-form-item label="邮箱" prop="email">
      <el-input v-model="form.email" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="onSubmit">提交</el-button>
      <el-button @click="onReset">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { addUser, getUserDetail, updateUser } from '@/api/user'

/** 表单数据 */
const form = ref({ username: '', email: '' })
/** 表单校验规则 */
const rules = { username: [{ required: true, message: '请输入用户名', trigger: 'blur' }] }
const formRef = ref()

/** 提交表单 */
const onSubmit = async () => {
  await formRef.value.validate()
  // ...判断新增或编辑，调用接口
}

/** 重置表单 */
const onReset = () => {
  form.value = { username: '', email: '' }
}

/** 编辑时回显数据 */
const fetchDetail = async (id) => {
  // ...接口调用
}

onMounted(() => {
  // ...如果是编辑，调用fetchDetail
})
</script>

<style scoped lang="scss">
// 页面样式
</style>
```

---

## 3. 详情页（如：用户详情、订单详情）

### 目录与命名规范

- 路径：`src/views/模块名/xxx.vue`
- 组件名、vue文件：大驼峰，如 `UserDetail.vue`
- ts后缀的文件名：小写+中划线，如 `user-detail.ts`

### 代码结构与内容要求

- 顶部注释：页面名称、功能描述
- 使用 `<script setup lang="ts">`
- 使用 `element-plus` 的 `el-descriptions`、`el-card` 等
- 支持数据回显、加载状态、错误处理
- 数据接口调用放在 `src/api/模块名/xxx.ts`
- 响应式数据、方法、生命周期
- 代码注释齐全

### 常用交互

- 数据展示（只读）
- 加载中、加载失败提示
- 返回按钮

### AI提示词模板

```
你是拥有20年前端开发经验的工程师，请生成一个【详情页】代码，要求如下：
1. 页面功能：如"展示用户详情，支持加载中、加载失败提示"
2. 路径：src/views/模块名/xxx.vue
3. 使用Vue3 + TypeScript + <script setup lang="ts">
4. 使用element-plus的el-descriptions、el-card等
5. 数据接口调用src/api/模块名/下的接口
6. 代码需有详细注释，复杂逻辑需块注释
7. 页面顶部有功能描述注释
8. 支持返回按钮
```

### 示例代码结构

```vue
<!--
  页面名称：用户详情
  功能描述：展示用户详情，支持加载中、加载失败提示
-->
<template>
  <el-card>
    <el-descriptions title="用户信息" :column="2" v-if="detail">
      <el-descriptions-item label="用户名">{{ detail.username }}</el-descriptions-item>
      <el-descriptions-item label="邮箱">{{ detail.email }}</el-descriptions-item>
    </el-descriptions>
    <el-empty v-else-if="error" description="加载失败" />
    <el-skeleton v-else :rows="3" animated />
    <el-button @click="onBack">返回</el-button>
  </el-card>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getUserDetail } from '@/api/user'
import { useRouter } from 'vue-router'

/** 详情数据 */
const detail = ref(null)
const error = ref(false)
const router = useRouter()

/** 获取详情 */
const fetchDetail = async (id) => {
  try {
    // ...接口调用
    error.value = false
  } catch {
    error.value = true
  }
}

const onBack = () => router.back()

onMounted(() => {
  // ...获取路由参数，调用fetchDetail
})
</script>

<style scoped lang="scss">
// 页面样式
</style>
```

---

项目目录架构可参考：.cursor\rules\grammar.rule

## 总结与建议

- 每类页面都应严格按照模板和注释规范开发，保证风格统一、易维护
- 建议将这些模板内容补充到 `README.md` 或 `提示词/` 目录下，便于团队查阅
- 如需其他类型页面（如弹窗、嵌套路由、复杂交互等）模板，可随时告知

如需进一步细化某一类页面的特殊场景（如带树形结构的列表、分步表单、带附件的详情页等），请直接说明需求！
