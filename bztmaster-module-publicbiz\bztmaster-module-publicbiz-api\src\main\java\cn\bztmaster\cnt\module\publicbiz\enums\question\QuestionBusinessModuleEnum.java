package cn.bztmaster.cnt.module.publicbiz.enums.question;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 考题业务模块枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum QuestionBusinessModuleEnum {

    DOMESTIC_SERVICE("家政业务", "家政服务相关考题"),
    UNIVERSITY_BUSINESS("高校业务", "高校实践相关考题"),
    TRAINING_BUSINESS("培训业务", "培训管理相关考题"),
    CERTIFICATION_BUSINESS("认证业务", "认证服务相关考题");

    /**
     * 业务模块值
     */
    private final String module;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据业务模块值获取枚举
     *
     * @param module 业务模块值
     * @return 枚举
     */
    public static QuestionBusinessModuleEnum getByModule(String module) {
        if (module == null) {
            return null;
        }
        for (QuestionBusinessModuleEnum value : QuestionBusinessModuleEnum.values()) {
            if (value.getModule().equals(module)) {
                return value;
            }
        }
        return null;
    }
}
