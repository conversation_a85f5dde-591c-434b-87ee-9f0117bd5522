<!--
  页面名称：场地操作日志抽屉
  功能描述：展示指定场地的操作日志，支持关闭，样式自适应
-->
<template>
  <el-drawer
    :model-value="props.visible"
    :title="props.siteName + ' 的操作日志'"
    size="540px"
    direction="rtl"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    @close="onClose"
    class="site-optlog-drawer"
    :with-header="true"
  >
    <el-timeline>
      <el-timeline-item
        v-for="(log, idx) in props.logs"
        :key="idx"
        :timestamp="log.time"
        placement="top"
        color="#409EFF"
      >
        <div class="log-meta">
          <span class="log-operator">{{ log.operator }}</span>
        </div>
        <div class="log-content">{{ log.content }}</div>
      </el-timeline-item>
    </el-timeline>
    <template #footer>
      <el-button @click="onClose" style="width:100%">关闭</el-button>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

/**
 * props:
 * visible: 是否显示抽屉
 * siteName: 场地名称
 * logs: 日志数组 [{time, operator, content}]
 */
const props = defineProps({
  visible: { type: Boolean, default: false },
  siteName: { type: String, default: '' },
  logs: {
    type: Array as () => Array<{ time: string; operator: string; content: string }>,
    default: () => []
  }
})

const emit = defineEmits(['close'])

const onClose = () => {
  emit('close')
}
</script>

<style scoped lang="scss">
.site-optlog-drawer {
  .el-drawer__header {
    font-size: 22px;
    font-weight: bold;
    padding-bottom: 8px;
    margin-bottom: 0;
  }
  .el-drawer__body {
    padding-top: 0;
    min-height: 120px;
    max-height: 400px;
    overflow-y: auto;
  }
  .el-timeline {
    margin-top: 8px;
  }
  .log-meta {
    color: #888;
    font-size: 14px;
    margin-bottom: 4px;
  }
  .log-operator {
    font-weight: 500;
  }
  .log-content {
    background: #f6f8fa;
    border-radius: 6px;
    padding: 10px 16px;
    font-size: 16px;
    color: #222;
    margin-bottom: 4px;
    word-break: break-all;
  }
}
@media (max-width: 600px) {
  .site-optlog-drawer {
    .el-drawer {
      width: 98vw !important;
      min-width: 0;
    }
    .el-drawer__header { font-size: 18px; }
    .log-content { font-size: 14px; }
  }
}
</style>
