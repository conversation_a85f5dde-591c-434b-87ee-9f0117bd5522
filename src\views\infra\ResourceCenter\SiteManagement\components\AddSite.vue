<!--
  页面名称：新增场地弹窗
  功能描述：新增场地，支持表单校验、动态座位类型、分组、重置、提交、取消，样式自适应
-->
<template>
  <el-drawer
    v-model="visible"
    title="新增场地"
    size="600px"
    direction="rtl"
    :with-header="true"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="onCancel"
    class="add-site-drawer"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="110px"
      label-position="top"
      class="add-site-form"
    >
      <!-- 基本信息 -->
      <el-divider content-position="left">基本信息</el-divider>
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="场地名称" prop="name" required>
            <el-input v-model="form.name" placeholder="请输入场地名称" maxlength="30" show-word-limit />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="24">
          <el-form-item label="所属校区/位置" prop="campus" required>
            <el-select v-model="form.campus" placeholder="请选择校区">
              <el-option v-for="item in campusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="场地类型" prop="type" required>
            <el-select v-model="form.type" placeholder="请选择场地类型">
              <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="24">
          <el-form-item label="具体位置" prop="location" required>
            <el-input v-model="form.location" placeholder="如：A座1楼101室" maxlength="30" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 座位配置管理 -->
      <el-divider content-position="left">座位配置管理</el-divider>
      <div class="seat-summary">
        总座位数：<span class="seat-total">{{ seatTotal }}</span> 座（自动计算）
      </div>
      <div class="seat-types">
        <div class="seat-types-header">
          <span>座位分类设置</span>
          <el-button type="primary" size="small" @click="addSeatType" style="margin-left: 12px;">+ 添加类型</el-button>
        </div>
        <div v-for="(seat, idx) in form.seatTypes" :key="idx" class="seat-type-row">
          <el-input v-model="seat.name" placeholder="类型名称（如：普通座）" style="width: 32%; margin-right: 8px;" maxlength="10" />
          <el-input-number v-model="seat.count" :min="0" placeholder="数量" style="width: 22%; margin-right: 8px;" />
          <el-input v-model="seat.remark" placeholder="备注/标签（可选）" style="width: 32%; margin-right: 8px;" maxlength="20" />
          <el-button icon="el-icon-delete" type="danger" size="small" @click="removeSeatType(idx)" circle />
        </div>
      </div>
      <div class="seat-tip">
        <el-icon style="vertical-align: middle;"><el-icon-info-filled /></el-icon>
        总座位数将根据各类型座位数量自动计算更新
      </div>

      <!-- 设备和状态 -->
      <el-divider content-position="left">设备和状态</el-divider>
      <el-row :gutter="16">
        <el-col :sm="16" :xs="24">
          <el-form-item label="设备配置" prop="equipment">
            <el-input v-model="form.equipment" type="textarea" :rows="2" placeholder="请描述场地的设备配置，如：投影仪、音响设备、空调等" maxlength="100" />
          </el-form-item>
        </el-col>
        <el-col :sm="8" :xs="24">
          <el-form-item label="状态" prop="status" required>
            <el-select v-model="form.status" placeholder="请选择状态">
              <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="场地描述" prop="desc">
        <el-input v-model="form.desc" type="textarea" :rows="2" placeholder="场地的详细描述和适用场景" maxlength="100" />
      </el-form-item>

      <!-- 负责人信息 -->
      <el-divider content-position="left">负责人信息</el-divider>
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="负责人" prop="manager" required>
            <el-input v-model="form.manager" placeholder="负责人姓名" maxlength="10" />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="24">
          <el-form-item label="联系电话" prop="managerPhone" required>
            <el-input v-model="form.managerPhone" placeholder="负责人联系电话" maxlength="20" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div style="text-align:right;margin-top:16px;">
      <el-button @click="onCancel">取消</el-button>
      <el-button type="primary" @click="onSubmit">保存</el-button>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { InfoFilled } from '@element-plus/icons-vue'

/**
 * props:
 * visible: 是否显示弹窗
 * data: 编辑时回显数据
 */
const props = defineProps({
  visible: { type: Boolean, default: false },
  data: { type: Object, default: () => ({}) }
})
const emit = defineEmits(['close', 'submit'])

const visible = ref(props.visible)
watch(() => props.visible, v => visible.value = v)
watch(visible, v => { if (!v) emit('close') })

const formRef = ref()
const form = ref({
  name: '',
  campus: '',
  type: '',
  location: '',
  seatTypes: [
    { name: '', count: 0, remark: '' }
  ],
  equipment: '',
  status: '',
  desc: '',
  manager: '',
  managerPhone: ''
})

const rules = {
  name: [{ required: true, message: '请输入场地名称', trigger: 'blur' }],
  campus: [{ required: true, message: '请选择校区', trigger: 'change' }],
  type: [{ required: true, message: '请选择场地类型', trigger: 'change' }],
  location: [{ required: true, message: '请输入具体位置', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
  manager: [{ required: true, message: '请输入负责人姓名', trigger: 'blur' }],
  managerPhone: [{ required: true, message: '请输入负责人联系电话', trigger: 'blur' }]
}

const campusOptions = [
  { label: '总部校区', value: '总部校区' },
  { label: '华东分校', value: '华东分校' }
]
const typeOptions = [
  { label: '培训教室', value: '培训教室' },
  { label: '会议室', value: '会议室' },
  { label: '考试场地', value: '考试场地' },
  { label: '实训室', value: '实训室' },
  { label: '多功能厅', value: '多功能厅' }
]
const statusOptions = [
  { label: '可用', value: '可用' },
  { label: '已预约', value: '已预约' },
  { label: '维护中', value: '维护中' },
  { label: '停用', value: '停用' }
]

/** 计算总座位数 */
const seatTotal = computed(() => form.value.seatTypes.reduce((sum, s) => sum + (Number(s.count) || 0), 0))

/** 添加座位类型 */
const addSeatType = () => {
  form.value.seatTypes.push({ name: '', count: 0, remark: '' })
}
/** 删除座位类型 */
const removeSeatType = (idx: number) => {
  if (form.value.seatTypes.length > 1) form.value.seatTypes.splice(idx, 1)
  else ElMessage.warning('至少保留一个座位类型')
}

/** 取消 */
const onCancel = () => {
  emit('close')
}
/** 提交 */
const onSubmit = () => {
  formRef.value.validate((valid: boolean) => {
    if (!valid) return
    emit('submit', { ...form.value, seatTotal: seatTotal.value })
  })
}
</script>

<style scoped lang="scss">
.add-site-drawer {
  .el-drawer__header { font-size: 22px; font-weight: bold; }
  .el-drawer__body { max-height: 70vh; overflow-y: auto; }
  .el-divider { margin: 18px 0 10px 0; }
  .seat-summary { color: #666; margin-bottom: 8px; }
  .seat-total { color: #409eff; font-weight: bold; }
  .seat-types-header { display: flex; align-items: center; margin-bottom: 8px; }
  .seat-type-row { display: flex; align-items: center; margin-bottom: 8px; }
  .seat-tip { color: #888; font-size: 13px; margin-bottom: 8px; }
}
@media (max-width: 800px) {
  .add-site-drawer .el-drawer { width: 98vw !important; min-width: 0; }
  .el-drawer__header { font-size: 18px; }
}
</style>
