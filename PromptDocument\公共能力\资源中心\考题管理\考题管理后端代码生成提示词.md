请结合`PromptDocument\公共能力\考题管理\考题管理接口文档.md`接口文档直接帮我生成后端代码，

\## 1. 自动生成方案说明

### 步骤一：分析页面
- 读取 `D:\Project\chuanneng\chuanneng-hr-service-vue\src\views\infra\ResourceCenter\QuestionManagement\` 下的所有页面，分析页面涉及的业务功能、表单字段、表格展示、操作按钮等。



\### 步骤二：页面设计的相关数据表
## 数据库表结构设计

### 1. 考题主表 (publicbiz_question)

```sql
-- 考题主表：存储考题的基本信息、题干、答案等核心数据
CREATE TABLE `publicbiz_question` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',

  -- 分类信息字段
  `level1_name` VARCHAR(64) NOT NULL COMMENT '一级分类名称，如：职业技能等级认定',
  `level1_code` VARCHAR(32) NOT NULL COMMENT '一级分类代码，如：ZY001',
  `level2_name` VARCHAR(64) NOT NULL COMMENT '二级分类名称，如：家政服务类',
  `level2_code` VARCHAR(32) NOT NULL COMMENT '二级分类代码，如：JZ001',
  `level3_name` VARCHAR(64) NOT NULL COMMENT '三级分类名称，如：家政服务员',
  `level3_code` VARCHAR(32) NOT NULL COMMENT '三级分类代码，如：JZFW001',

  -- 认定点信息
  `cert_name` VARCHAR(128) NOT NULL COMMENT '认定点名称，如：职业道德基础',
  `cert_code` VARCHAR(32) NOT NULL COMMENT '认定点代码，如：KP001',

  -- 题目内容
  `title` TEXT NOT NULL COMMENT '题干内容',
  `type` VARCHAR(32) NOT NULL COMMENT '题型：单选题、多选题、判断题、简答题、填空题、材料题、排序题、匹配题、文件上传题',
  `answer` TEXT NOT NULL COMMENT '参考答案',

  -- 业务分类
  `biz` VARCHAR(64) NOT NULL COMMENT '业务模块：家政业务、高校业务、培训业务、认证业务',
  `biz_name` VARCHAR(64) COMMENT '业务模块名称',

  -- 扩展字段
  `difficulty` TINYINT DEFAULT 1 COMMENT '难度等级：1-简单，2-中等，3-困难',
  `score` DECIMAL(5,2) DEFAULT 0.00 COMMENT '题目分值',
  `time_limit` INT DEFAULT 0 COMMENT '答题时间限制（秒），0表示无限制',
  `explanation` TEXT COMMENT '题目解析',
  `keywords` VARCHAR(255) COMMENT '关键词，用逗号分隔',
  `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',

  -- 公共字段
  `creator` VARCHAR(64) COMMENT '创建人',
  `creator_name` VARCHAR(64) COMMENT '创建人姓名',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 索引
  INDEX `idx_tenant_id` (`tenant_id`),
  INDEX `idx_level1_name` (`level1_name`),
  INDEX `idx_level2_name` (`level2_name`),
  INDEX `idx_level3_name` (`level3_name`),
  INDEX `idx_type` (`type`),
  INDEX `idx_biz` (`biz`),
  INDEX `idx_status` (`status`),
  INDEX `idx_create_time` (`create_time`),
  INDEX `idx_creator` (`creator`),
  FULLTEXT INDEX `idx_title_content` (`title`, `answer`) WITH PARSER ngram
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考题主表';
```

### 2. 考题分类表 (publicbiz_question_category)

```sql
-- 考题分类表：存储考题的分类层级结构和代码信息
CREATE TABLE `publicbiz_question_category` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',

  -- 分类层级信息
  `level1_name` VARCHAR(64) NOT NULL COMMENT '一级分类名称',
  `level1_code` VARCHAR(32) NOT NULL COMMENT '一级分类代码',
  `level2_name` VARCHAR(64) COMMENT '二级分类名称',
  `level2_code` VARCHAR(32) COMMENT '二级分类代码',
  `level3_name` VARCHAR(64) COMMENT '三级分类名称',
  `level3_code` VARCHAR(32) COMMENT '三级分类代码',

  -- 认定点信息
  `cert_name` VARCHAR(128) COMMENT '认定点名称',
  `cert_code` VARCHAR(32) COMMENT '认定点代码',

  -- 业务分类
  `biz` VARCHAR(64) NOT NULL COMMENT '业务模块：家政业务、高校业务、培训业务、认证业务',
  `biz_name` VARCHAR(64) COMMENT '业务模块名称',

  -- 分类属性
  `parent_id` BIGINT DEFAULT 0 COMMENT '父级分类ID，0表示顶级分类',
  `level` TINYINT NOT NULL DEFAULT 1 COMMENT '分类层级：1-一级，2-二级，3-三级',
  `sort_order` INT DEFAULT 0 COMMENT '排序序号',
  `description` TEXT COMMENT '分类描述',
  `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',

  -- 公共字段
  `creator` VARCHAR(64) COMMENT '创建人',
  `creator_name` VARCHAR(64) COMMENT '创建人姓名',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 索引
  INDEX `idx_tenant_id` (`tenant_id`),
  INDEX `idx_parent_id` (`parent_id`),
  INDEX `idx_level` (`level`),
  INDEX `idx_biz` (`biz`),
  INDEX `idx_status` (`status`),
  UNIQUE INDEX `uk_category_code` (`tenant_id`, `level1_code`, `level2_code`, `level3_code`, `deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考题分类表';
```

### 3. 考题选项表 (publicbiz_question_option)

```sql
-- 考题选项表：存储选择题、匹配题、排序题等的选项信息
CREATE TABLE `publicbiz_question_option` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',

  -- 关联信息
  `question_id` BIGINT NOT NULL COMMENT '考题ID，关联publicbiz_question表',

  -- 选项信息
  `option_type` VARCHAR(16) NOT NULL COMMENT '选项类型：choice-选择项，match_left-匹配左列，match_right-匹配右列',
  `option_key` VARCHAR(8) NOT NULL COMMENT '选项标识，如：A、B、C、D或1、2、3、4',
  `option_content` TEXT NOT NULL COMMENT '选项内容',
  `is_correct` TINYINT(1) DEFAULT 0 COMMENT '是否正确答案：0-否，1-是',
  `sort_order` INT DEFAULT 0 COMMENT '排序序号',

  -- 匹配题专用字段
  `match_target` VARCHAR(8) COMMENT '匹配目标，用于匹配题记录对应关系',

  -- 公共字段
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 索引
  INDEX `idx_tenant_id` (`tenant_id`),
  INDEX `idx_question_id` (`question_id`),
  INDEX `idx_option_type` (`option_type`),
  INDEX `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考题选项表';
```


\### 步骤三：确定后端接口与分层结构

\- 每个API和页面功能，生成一个对应的后端Controller（RESTful风格）。

\- 但是每个接口都必须带有动词前缀，例如page、get、list、create、update、delete。

\- 每个业务实体，生成VO/DTO、Service接口与实现、Mapper接口（如涉及数据库）、Convert类、枚举、API接口等。

\- 目录结构严格参考 bztmaster-module-system项目，每一层都需要，具体目录格式如下：

bztmaster-module-publicbiz/
├── bztmaster-module-publicbiz-api/
│   ├── pom.xml
│   ├── .flattened-pom.xml
│   └── src/
│       └── main/
│           └── java/
│               └── cn/
│                   └── bztmaster/
│                       └── cnt/
│                           └── module/
│                               └── publicbiz/
│                                   └── api/
│                                       └── question/
│                                           ├── QuestionApi.java
│                                           └── dto/
│                                               ├── QuestionRespDTO.java
│                                               ├── QuestionSaveReqDTO.java
│                                               ├── QuestionPageReqDTO.java
│                                               └── ...（其它DTO/VO/ReqVO等）
│                                   └── enums/
│                                       └── ...（如有枚举）
├── bztmaster-module-publicbiz-server/
│   ├── pom.xml
│   ├── .flattened-pom.xml
│   ├── Dockerfile
│   └── src/
│       ├── main/
│       │   ├── java/
│       │   │   └── cn/
│       │   │       └── bztmaster/
│       │   │           └── cnt/
│       │   │               └── module/
│       │   │                   └── publicbiz/
│       │   │                       ├── controller/
│       │   │                       │   └── admin/
│       │   │                       │       └── question/
│       │   │                       │           ├── QuestionController.java
│       │   │                       │           └── vo/
│       │   │                       │               ├── QuestionRespVO.java
│       │   │                       │               ├── QuestionSaveReqVO.java
│       │   │                       │               ├── QuestionPageReqVO.java
│       │   │                       │               └── ...（其它VO/ReqVO等）
│       │   │                       ├── dal/
│       │   │                       │   ├── mysql/
│       │   │                       │   │   └── question/
│       │   │                       │   │       └── QuestionMapper.java
│       │   │                       │   ├── dataobject/
│       │   │                       │   │   └── question/
│       │   │                       │   │       └── QuestionDO.java
│       │   │                       ├── service/
│       │   │                       │   └── question/
│       │   │                       │       ├── impl/
│       │   │                       │       │   └── QuestionServiceImpl.java
│       │   │                       │       └── QuestionService.java
│       │   │                       ├── convert/
│       │   │                       │   └── question/
│       │   │                       │       └── QuestionConvert.java
│       │   │                       └── ...（其它如 util、framework、job、mq 等可选）
│       │   └── resources/
│       │       └── mapper/
│       │           └── QuestionMapper.xml
│       │       └── application.yaml
│       │       └── ...（其它配置文件）
│       └── test/
│           └── java/
│               └── ...（测试代码结构）
├── pom.xml
└── .flattened-pom.xml



\### 步骤四：自动生成代码

\- Controller：每个API生成一个对应的Controller方法，路径、方法、参数与前端一致，带有Swagger注解。

\- VO/DTO：根据前端参数和表单字段生成请求VO、响应VO，带有参数校验注解。

\- Service/Impl：生成接口和实现，包含CRUD和具体的业务方法，方法注释详细。

\- Mapper/DO：如涉及数据库，生成MyBatis Mapper接口和DO实体，字段与VO/DTO一致。

\- Convert：生成VO与DO、DTO的转换类。

\- Enums：如有枚举字段，自动生成枚举类。

\- API接口：如有远程调用需求，生成Feign接口。

\- 所有类和方法都带有详细注释，对应DTO、VO与DO对象也生成详细注释，便于初学者理解。

\- 前端与后端对应的字段信息一定要确保能对应一致。

  

\### 步骤五：自动补全与TODO

\- 对于前端未明确的字段类型、业务逻辑，自动推断或以// TODO注释标记，便于后续补充。

\- 生成后的代码直接更新到指定的项目bztmaster-module-publicbiz模块下对应的目录中。 



