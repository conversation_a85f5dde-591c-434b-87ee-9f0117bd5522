<!--
  页面名称：线索中心首页
  功能描述：展示线索列表，支持多条件搜索、分页、编辑、分配、跟进、删除等操作
-->
<template>
  <div class="clue-center">
    <!-- 顶部搜索栏 -->
    <el-form :inline="true" :model="searchForm" class="search-form" @submit.prevent>
      <el-form-item label="线索来源：">
        <el-select
          v-model="searchForm.leadSource"
          placeholder="全部"
          clearable
          style="width: 120px"
        >
          <el-option label="全部" :value="''" />
          <el-option
            v-for="item in leadSourceOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="线索状态：">
        <el-select
          v-model="searchForm.leadStatus"
          placeholder="全部"
          clearable
          style="width: 120px"
        >
          <el-option label="全部" :value="''" />
          <el-option
            v-for="item in leadStatusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="业务模块：">
        <el-select
          v-model="searchForm.businessModule"
          placeholder="全部"
          clearable
          style="width: 120px"
        >
          <el-option label="全部" :value="''" />
          <el-option
            v-for="item in businessModuleOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="searchForm.keyword"
          placeholder="搜索客户姓名/电话..."
          clearable
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">筛选</el-button>
        <el-button @click="onReset">重置</el-button>
      </el-form-item>
      <el-form-item style="float: right; margin-left: auto">
        <el-button type="primary" @click="onCreate" plain>+ 新建线索</el-button>
      </el-form-item>
    </el-form>

    <!-- 线索列表表格 -->
    <el-table :data="tableData" style="width: 100%; margin-top: 16px" border>
      <el-table-column prop="leadId" label="线索ID" width="130" />
      <el-table-column prop="customerName" label="客户姓名" min-width="100" />
      <el-table-column prop="customerPhone" label="联系电话" min-width="120" />
      <el-table-column prop="leadSourceDesc" label="线索来源" min-width="100" />
      <el-table-column prop="businessModuleDesc" label="业务模块" min-width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.businessModuleDesc === '家政业务'" type="success"
            >家政业务</el-tag
          >
          <el-tag v-else-if="scope.row.businessModuleDesc === '高校业务'" type="info"
            >高校业务</el-tag
          >
          <el-tag v-else-if="scope.row.businessModuleDesc === '培训业务'" type="warning"
            >培训业务</el-tag
          >
          <el-tag v-else-if="scope.row.businessModuleDesc === '认证业务'" type="danger"
            >认证业务</el-tag
          >
          <span v-else>{{ scope.row.businessModuleDesc }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="leadStatusDesc" label="线索状态" min-width="90" />
      <el-table-column prop="createMethodDesc" label="创建方式" min-width="100" />
      <el-table-column prop="creatorName" label="创建人" min-width="100">
        <template #default="scope">
          <span>{{ scope.row.creatorName || scope.row.creator || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="currentOwnerName" label="当前跟进人" min-width="100">
        <template #default="scope">
          <span>{{ scope.row.currentOwnerName || scope.row.currentOwner || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="createTimeFormatted" label="创建时间" min-width="100" />
      <el-table-column label="操作" fixed="right" width="260">
        <template #default="scope">
          <el-button size="small" @click="onEdit(scope.row)">编辑</el-button>
          <el-button size="small" @click="onAssign(scope.row)">分配</el-button>
          <el-button size="small" @click="onFollow(scope.row)">跟进</el-button>
          <el-button size="small" type="danger" @click="onDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pagination.page"
      v-model:page-size="pagination.pageSize"
      :total="pagination.total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      style="margin-top: 16px; text-align: right"
      @current-change="fetchList"
      @size-change="fetchList"
    />
    <AddClue ref="addClueRef" @success="fetchList" />
    <FollowUpClue ref="followUpClueRef" />
    <DistributeClue ref="distributeClueRef" @success="fetchList" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import AddClue from './components/AddClue.vue'
import FollowUpClue from './components/FollowUpClue.vue'
import DistributeClue from './components/DistributeClue.vue'
import { ClueCenterApi, type ClueListItem } from '@/api/infra/clueCenter'

const addClueRef = ref()
const followUpClueRef = ref()
const distributeClueRef = ref()

/** 搜索表单数据 */
const searchForm = ref({
  leadSource: '',
  leadStatus: '',
  businessModule: '',
  keyword: ''
})

/** 表格数据（本地测试数据） */
const tableData = ref<ClueListItem[]>([])

/** 分页信息 */
const pagination = ref({ page: 1, pageSize: 10, total: 12 })

/** 下拉选项（本地静态） */
const leadSourceOptions = ref([
  { label: '官网注册', value: '1' },
  { label: '市场活动', value: '2' },
  { label: '公众号文章', value: '3' },
  { label: '视频号', value: '4' },
  { label: '抖音', value: '5' },
  { label: '客户推荐', value: '6' },
  { label: '电话营销', value: '7' },
  { label: '社交媒体', value: '8' },
  { label: '展会', value: '9' },
  { label: '其他', value: '99' }
])
const businessModuleOptions = ref([
  { label: '家政业务', value: '2' },
  { label: '高校业务', value: '1' },
  { label: '培训业务', value: '3' },
  { label: '认证业务', value: '4' }
])
const leadStatusOptions = ref([
  { label: '未处理', value: '1' },
  { label: '跟进中', value: '2' },
  { label: '已转化', value: '3' },
  { label: '无意向', value: '4' }
])

/** 获取线索列表（本地不做任何操作） */
const fetchList = async () => {
  // 示例调用
  const res = await ClueCenterApi.getClueListPage({
    page: pagination.value.page,
    pageSize: pagination.value.pageSize,
    leadSource: searchForm.value.leadSource,
    leadStatus: searchForm.value.leadStatus,
    businessModule: searchForm.value.businessModule,
    keyword: searchForm.value.keyword
  })
  tableData.value = res.list
  pagination.value.total = res.total
}

/** 搜索 */
const onSearch = () => {
  // 本地静态数据不做筛选
  fetchList()
}
/** 重置 */
const onReset = () => {
  searchForm.value = { leadSource: '', leadStatus: '', businessModule: '', keyword: '' }
  fetchList()
}
/** 新建线索 */
const onCreate = () => {
  addClueRef.value && addClueRef.value.open()
}
/** 编辑 */
const onEdit = (row: any) => {
  addClueRef.value && addClueRef.value.open(row)
}
/** 分配 */
const onAssign = (row: any) => {
  distributeClueRef.value && distributeClueRef.value.open(row)
}
/** 跟进 */
const onFollow = (row: any) => {
  followUpClueRef.value && followUpClueRef.value.open(row)
}
/** 删除 */
const onDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除线索"${row.customerName}"吗？删除后不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    // 调用删除API
    await ClueCenterApi.deleteClue({ id: row.id })

    // 删除成功后从本地数据中移除
    tableData.value = tableData.value.filter((item) => item.id !== row.id)
    pagination.value.total = tableData.value.length
    ElMessage.success('删除成功')
  } catch (error) {
    if (error === 'cancel') {
      // 用户取消删除，不显示错误信息
      return
    }
    console.error('删除线索失败:', error)
    ElMessage.error('删除失败，请重试')
  }
}

onMounted(() => {
  // 不请求任何接口
  fetchList()
})
</script>

<style scoped lang="scss">
.clue-center {
  padding: 24px;
  background: #fff;
  min-height: 100vh;
}
.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 8px;
}
</style>
