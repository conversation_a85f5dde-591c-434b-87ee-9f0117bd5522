<!--
  页面名称：题目操作日志
  功能描述：展示题目操作日志详情，支持加载、失败提示、返回按钮
-->
<template>
  <el-card class="opt-log-card">
    <div class="opt-log-header">
      <span class="opt-log-title">"{{ title }}" 的操作日志</span>
      <el-icon class="opt-log-close" @click="onClose"><Close /></el-icon>
    </div>
    <el-divider class="opt-log-divider" />
    <el-scrollbar height="calc(100vh - 120px)">
      <div v-if="loading">
        <el-skeleton :rows="3" animated />
      </div>
      <div v-else-if="error">
        <el-empty description="加载失败" />
      </div>
      <div v-else>
        <div v-for="(log, idx) in logs" :key="idx" class="opt-log-item">
          <div class="opt-log-meta">
            <el-icon class="opt-log-dot"><ChatDotRound /></el-icon>
            <span class="opt-log-date">{{ log.date }} by {{ log.user }}</span>
          </div>
          <div class="opt-log-content">{{ log.content }}</div>
        </div>
      </div>
    </el-scrollbar>
  </el-card>
</template>

<script setup lang="ts">
import { ref, onMounted, defineEmits } from 'vue'
import { Close, ChatDotRound } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

/** 日志标题（题干） */
const title = ref('家政服务员职业道德的核心要求是什么？ ...')
/** 日志数据 */
const logs = ref([
  {
    date: '2024-01-15',
    user: '张老师',
    content: '创建考题 "家政服务员职业道德的核心要求是什么？"'
  }
])
const loading = ref(false)
const error = ref(false)

const emit = defineEmits(['close'])

/** 关闭操作日志抽屉/弹窗 */
const onClose = () => {
  emit('close')
}

onMounted(() => {
  // 可在此处加载日志数据
  // loading.value = true
  // ...接口调用
  // loading.value = false
})
</script>

<style scoped lang="scss">
.opt-log-card {
  border-radius: 0;
  box-shadow: none;
  padding: 0 0 16px 0;
  min-height: 100vh;
}
.opt-log-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px 0 24px;
}
.opt-log-title {
  font-size: 18px;
  font-weight: bold;
}
.opt-log-close {
  cursor: pointer;
  font-size: 20px;
}
.opt-log-divider {
  margin: 8px 0 0 0;
}
.opt-log-item {
  margin: 24px 0 0 0;
  padding: 0 32px;
}
.opt-log-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #888;
  font-size: 13px;
}
.opt-log-dot {
  color: #409EFF;
  font-size: 16px;
}
.opt-log-date {
  font-size: 13px;
}
.opt-log-content {
  margin-left: 24px;
  margin-top: 8px;
  font-size: 15px;
  color: #333;
  background: #f7f8fa;
  border-radius: 4px;
  padding: 10px 16px;
  word-break: break-all;
}
</style>
