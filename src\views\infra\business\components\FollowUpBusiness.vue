<template>
  <el-drawer
    v-model="visible"
    :title="`${businessInfo.name}的跟进记录`"
    direction="rtl"
    size="500px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <!-- 商机信息 -->
    <div class="business-info-card">
      <div class="info-title">商机信息</div>
      <el-card shadow="never" class="info-card">
        <div class="info-row"><span class="label">商机名称：</span>{{ businessInfo.name }}</div>
        <div class="info-row"
          ><span class="label">关联客户：</span>{{ businessInfo.customerName }}</div
        >
        <div class="info-row"
          ><span class="label">业务模块：</span>
          <el-tag type="info" v-if="businessInfo.businessTypeName === '高校业务'">高校业务</el-tag>
          <el-tag type="warning" v-else-if="businessInfo.businessTypeName === '培训业务'"
            >培训业务</el-tag
          >
          <el-tag type="success" v-else-if="businessInfo.businessTypeName === '认证业务'"
            >认证业务</el-tag
          >
          <span v-else>{{ businessInfo.businessTypeName }}</span>
        </div>
        <div class="info-row"
          ><span class="label">商机金额：</span>{{ formatMoney(businessInfo.totalPrice) }}元</div
        >
        <div class="info-row"
          ><span class="label">销售阶段：</span>{{ businessInfo.businessStageName }}</div
        >
        <div class="info-row"
          ><span class="label">销售负责人：</span>{{ businessInfo.ownerUserName }}</div
        >
      </el-card>
    </div>

    <!-- 添加跟进记录 -->
    <div class="add-followup-section">
      <div class="section-title">添加跟进记录</div>
      <el-input
        v-model="followUpContent"
        type="textarea"
        :rows="3"
        maxlength="200"
        show-word-limit
        placeholder="在此输入跟进内容，例如：已电话沟通，客户对方案表示满意，准备进入商务谈判阶段。"
        style="margin-bottom: 8px"
      />
      <el-button type="primary" @click="addFollowUp" :disabled="!followUpContent.trim()"
        >添加跟进</el-button
      >
    </div>

    <!-- 跟进记录时间线 -->
    <el-timeline class="followup-timeline">
      <el-timeline-item
        v-for="(item, idx) in followUpList"
        :key="idx"
        :timestamp="item.time"
        placement="top"
      >
        <div>
          <b>{{ item.user }}</b
          >：{{ item.content }}
        </div>
      </el-timeline-item>
    </el-timeline>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, defineExpose } from 'vue'
import { ElMessage } from 'element-plus'
import request from '@/config/axios'

const visible = ref(false)
const businessInfo = reactive({
  name: '',
  customerName: '',
  businessTypeName: '',
  totalPrice: 0,
  businessStageName: '',
  ownerUserName: '',
  id: ''
})
const followUpContent = ref('')
const followUpList = ref<any[]>([])

async function open(business: any) {
  visible.value = true
  // 获取详情（含跟进记录）
  const detailRes = await request.get({
    url: '/publicbiz/business/detail',
    params: { id: business.id }
  })
  const detail = detailRes?.business || detailRes?.data?.business || {}
  Object.assign(businessInfo, {
    id: detail.id,
    name: detail.name,
    customerName: detail.customerName,
    businessTypeName: detail.businessType,
    totalPrice: detail.totalPrice,
    businessStageName: detail.businessStage,
    ownerUserName: detail.ownerUserName
  })
  // 跟进记录
  const followups = detailRes?.followups || detailRes?.data?.followups || []
  followUpList.value = (followups || []).map((item) => ({
    time: formatDate(item.followTime || item.createTime),
    user: item.followUserName || item.creator || '',
    content: item.content
  }))
}
function close() {
  visible.value = false
}

async function addFollowUp() {
  if (!followUpContent.value.trim()) return
  await request.post({
    url: '/publicbiz/business/followup/create',
    data: {
      businessId: businessInfo.id,
      content: followUpContent.value.trim()
    }
  })
  ElMessage.success('添加成功')
  followUpContent.value = ''
  // 重新拉取跟进记录
  open({ id: businessInfo.id })
}

function formatMoney(val: number) {
  if (!val) return '0'
  return val.toLocaleString('zh-CN')
}

function formatDate(val: string | number | Date) {
  if (!val) return ''
  const d = new Date(val)
  const pad = (n: number) => (n < 10 ? '0' + n : n)
  return `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} ${pad(d.getHours())}:${pad(d.getMinutes())}`
}

defineExpose({ open, close })
</script>

<style scoped lang="scss">
.business-info-card {
  margin-bottom: 16px;
}
.info-title {
  font-weight: bold;
  margin-bottom: 4px;
}
.info-card {
  font-size: 14px;
  background: #f8fafd;
  border-radius: 6px;
  padding: 12px 16px;
}
.info-row {
  margin-bottom: 4px;
  line-height: 1.8;
}
.label {
  color: #888;
  min-width: 80px;
  display: inline-block;
}
.add-followup-section {
  margin-bottom: 16px;
}
.section-title {
  font-weight: bold;
  margin-bottom: 4px;
}
.followup-timeline {
  margin-top: 8px;
}
</style>
