<template>
  <div>
    <div class="talent-detail-wrapper" v-if="detailData">
      <el-divider class="divider" />
      <div class="profile-row">
        <el-avatar size="80" class="avatar">{{ detailData.user?.name?.charAt(0) }}</el-avatar>
        <div class="profile-info">
          <div class="profile-name">
            {{ detailData.user?.name }}
            <el-tag v-for="tag in realNameTags" :key="tag.tagId" type="success" size="small" class="real-tag">{{ tag.tagName }}</el-tag>
          </div>
          <div class="profile-base-info">
            <div><span>身份证：{{ detailData.user?.identityId }}</span></div>
            <div><span>手机号：{{ detailData.user?.phone }}</span> <span class="divider-bar">|</span> <span>邮箱：{{ detailData.user?.email }}</span></div>
            <div><span>出生日期：{{ detailData.user?.birthDate }}</span> <span class="divider-bar">|</span> <span>性别：{{ detailData.user?.gender }}</span></div>
            <div><span>所属机构：{{ detailData.user?.orgName || '-' }}</span></div>
            <div><span>人才来源：{{ detailData.user?.talentSource || '-' }}</span></div>
          </div>
        </div>
      </div>
      <div class="section-title">人才标签云</div>
      <div class="tag-cloud">
        <el-tag v-for="tag in detailData.userTagList" :key="tag.tagId" class="tag-item" type="info">{{ tag.tagName }}</el-tag>
      </div>
      <el-divider class="divider" />
      <div class="section-title" style="margin-top: 24px;">生命周期时间轴</div>
      <el-timeline class="timeline-box">
        <el-timeline-item v-for="item in detailData.timelineList" :key="item.date + item.type" :timestamp="item.date">
          <b>[{{ item.type }}]</b> {{ item.content }}
        </el-timeline-item>
      </el-timeline>
      <el-divider class="divider" />
      <div class="detail-archive-card">
        <div class="archive-title">详细档案</div>
        <el-tabs v-model="activeTab" class="archive-tabs">
          <el-tab-pane label="教育背景" name="education">
            <div v-for="edu in detailData.educationList" :key="edu.educationId" class="edu-block">
              <div class="edu-school">{{ edu.institution }}</div>
              <div class="edu-info">学位类型: {{ edu.degreeType }}　专业: {{ edu.major }}　在校时间: {{ edu.startDate }} ~ {{ edu.endDate }}　学业排名: {{ edu.academicRanking }}</div>
              <el-divider class="edu-divider" />
            </div>
          </el-tab-pane>
          <el-tab-pane label="实践与项目" name="practice">
            <div v-for="practice in detailData.campusPracticeList" :key="practice.practiceId" class="practice-block">
              <div class="practice-title">[校园实践] {{ practice.practiceName }}</div>
              <div class="practice-info">组织方: {{ practice.organizer }}　时间: {{ practice.startDate }} ~ {{ practice.endDate }}</div>
              <div class="practice-desc">总结: {{ practice.practiceReport }}</div>
            </div>
            <div v-for="intern in detailData.internshipList" :key="intern.internshipId" class="practice-block">
              <div class="practice-title">[实习] {{ intern.company }}</div>
              <div class="practice-info">岗位: {{ intern.position }}　时间: {{ intern.startDate }} ~ {{ intern.endDate }}</div>
              <div class="practice-desc">职责: {{ intern.responsibilities }}</div>
            </div>
            <div v-for="project in detailData.projectList" :key="project.projectId" class="practice-block">
              <div class="practice-title">[项目] {{ project.name }}</div>
              <div class="practice-desc">描述: {{ project.description }}</div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="培训与技能" name="training">
            <div v-for="train in detailData.trainingList" :key="train.trainingId" class="train-block">
              <div class="train-title">[培训] {{ train.course }}</div>
              <div class="train-info">培训机构: {{ train.provider }}　完成日期: {{ train.completeDate }}</div>
            </div>
            <div v-for="skill in detailData.skillList" :key="skill.skillId" class="train-block">
              <div class="train-title">[技能] {{ skill.name }}</div>
              <div class="train-info">掌握程度: {{ skill.level }}</div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="认证与资质" name="certificate">
            <div v-for="cert in detailData.certificateList" :key="cert.certificateId" class="cert-block">
              <div class="cert-title">{{ cert.name }}</div>
              <div class="cert-info">
                证书编号: {{ cert.certificateNo || cert.certNo || '-' }}　
                发证机构: {{ cert.issuer }}　
                颁发日期: {{ cert.issueDate }}　
                到期时间: {{ cert.expiryDate || cert.expireDate || '-' }}　
                记录来源: {{ cert.source }}
              </div>
              <div v-if="cert.certificateImageUrl" class="cert-image">
                <el-image :src="cert.certificateImageUrl" style="width: 80px; height: 80px; object-fit: contain; border: 1px solid #eee; margin: 4px 0;" :preview-src-list="[cert.certificateImageUrl]" />
              </div>
              <div class="cert-status" :class="cert.status === 'VERIFIED' ? 'pass' : cert.status === 'PENDING_VERIFICATION' ? 'wait' : 'reject'">
                审核状态: {{ cert.status === 'VERIFIED' ? '已认证' : cert.status === 'PENDING_VERIFICATION' ? '待认证' : '已驳回' }}
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="求职与工作" name="job">
            <div v-for="job in detailData.employmentList" :key="job.employmentId" class="job-block">
              <div class="job-title">[工作] {{ job.company }}</div>
              <div class="job-info">职位: {{ job.position }}　任职时间: {{ job.startDate }} ~ {{ job.endDate || '至今' }}　薪资: {{ job.salary }}元/月</div>
            </div>
            <div v-for="apply in detailData.jobApplicationList" :key="apply.applicationId" class="job-block">
              <div class="job-title">[求职] {{ apply.company }}</div>
              <div class="job-info">申请岗位: {{ apply.position }}　申请日期: {{ apply.applyDate }}　状态: {{ apply.status }}</div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="用户评价" name="comment">
            <div v-for="comment in detailData.commentList" :key="comment.date + comment.role + comment.service_name" class="comment-block">
              <div class="comment-title">{{ comment.role }}: {{ comment.reviewer_name || comment.name }}<span v-if="comment.service_type === '订单'"> (订单: {{ comment.service_name }})</span><span v-else-if="comment.service_type === '课程'"> (课程: {{ comment.service_name }})</span></div>
              <div class="comment-info">服务项目: {{ comment.service || comment.service_name }}　评价时间: {{ comment.date || comment.comment_date }}　评分: <span class="star">{{ '★★★★★'.slice(0, comment.score) }}</span></div>
              <div class="comment-content">{{ comment.content }}</div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="footer-btn">
        <el-button type="primary" @click="exportPdf"><el-icon><Document /></el-icon>导出简历</el-button>
      </div>
    </div>
    <!-- 导出专用平铺内容区域 -->
    <div ref="pdfAllTabsRef" class="talent-detail-wrapper" style="display:none" v-if="detailData">
      <el-divider class="divider" />
      <div class="profile-row">
        <el-avatar size="80" class="avatar">{{ detailData.user?.name?.charAt(0) }}</el-avatar>
        <div class="profile-info">
          <div class="profile-name">
            {{ detailData.user?.name }}
            <el-tag v-for="tag in realNameTags" :key="tag.tagId" type="success" size="small" class="real-tag">{{ tag.tagName }}</el-tag>
          </div>
          <div class="profile-base-info">
            <div><span>身份证：{{ detailData.user?.identityId }}</span></div>
            <div><span>手机号：{{ detailData.user?.phone }}</span> <span class="divider-bar">|</span> <span>邮箱：{{ detailData.user?.email }}</span></div>
            <div><span>出生日期：{{ detailData.user?.birthDate }}</span> <span class="divider-bar">|</span> <span>性别：{{ detailData.user?.gender }}</span></div>
            <div><span>所属机构：{{ detailData.user?.orgName || '-' }}</span></div>
            <div><span>人才来源：{{ detailData.user?.talentSource || '-' }}</span></div>
          </div>
        </div>
      </div>
      <div class="section-title">人才标签云</div>
      <div class="tag-cloud">
        <el-tag v-for="tag in detailData.userTagList" :key="tag.tagId" class="tag-item" type="info">{{ tag.tagName }}</el-tag>
      </div>
      <el-divider class="divider" />
      <div class="section-title" style="margin-top: 24px;">生命周期时间轴</div>
      <el-timeline class="timeline-box">
        <el-timeline-item v-for="item in detailData.timelineList" :key="item.date + item.type" :timestamp="item.date">
          <b>[{{ item.type }}]</b> {{ item.content }}
        </el-timeline-item>
      </el-timeline>
      <el-divider class="divider" />
      <div class="detail-archive-card">
        <div class="archive-title">详细档案</div>
        <!-- 平铺所有tab内容 -->
        <div>
          <div class="section-title">教育背景</div>
          <div v-for="edu in detailData.educationList" :key="edu.educationId" class="edu-block">
            <div class="edu-school">{{ edu.institution }}</div>
            <div class="edu-info">学位类型: {{ edu.degreeType }}　专业: {{ edu.major }}　在校时间: {{ edu.startDate }} ~ {{ edu.endDate }}　学业排名: {{ edu.academicRanking }}</div>
            <el-divider class="edu-divider" />
          </div>
          <div class="section-title">实践与项目</div>
          <div v-for="practice in detailData.campusPracticeList" :key="practice.practiceId" class="practice-block">
            <div class="practice-title">[校园实践] {{ practice.practiceName }}</div>
            <div class="practice-info">组织方: {{ practice.organizer }}　时间: {{ practice.startDate }} ~ {{ practice.endDate }}</div>
            <div class="practice-desc">总结: {{ practice.practiceReport }}</div>
          </div>
          <div v-for="intern in detailData.internshipList" :key="intern.internshipId" class="practice-block">
            <div class="practice-title">[实习] {{ intern.company }}</div>
            <div class="practice-info">岗位: {{ intern.position }}　时间: {{ intern.startDate }} ~ {{ intern.endDate }}</div>
            <div class="practice-desc">职责: {{ intern.responsibilities }}</div>
          </div>
          <div v-for="project in detailData.projectList" :key="project.projectId" class="practice-block">
            <div class="practice-title">[项目] {{ project.name }}</div>
            <div class="practice-desc">描述: {{ project.description }}</div>
          </div>
          <div class="section-title">培训与技能</div>
          <div v-for="train in detailData.trainingList" :key="train.trainingId" class="train-block">
            <div class="train-title">[培训] {{ train.course }}</div>
            <div class="train-info">培训机构: {{ train.provider }}　完成日期: {{ train.completeDate }}</div>
          </div>
          <div v-for="skill in detailData.skillList" :key="skill.skillId" class="train-block">
            <div class="train-title">[技能] {{ skill.name }}</div>
            <div class="train-info">掌握程度: {{ skill.level }}</div>
          </div>
          <div class="section-title">认证与资质</div>
          <div v-for="cert in detailData.certificateList" :key="cert.certificateId" class="cert-block">
            <div class="cert-title">{{ cert.name }}</div>
            <div class="cert-info">
              证书编号: {{ cert.certificateNo || cert.certNo || '-' }}　
              发证机构: {{ cert.issuer }}　
              颁发日期: {{ cert.issueDate }}　
              到期时间: {{ cert.expiryDate || cert.expireDate || '-' }}　
              记录来源: {{ cert.source }}
            </div>
            <div v-if="cert.certificateImageUrl" class="cert-image">
              <el-image :src="cert.certificateImageUrl" style="width: 80px; height: 80px; object-fit: contain; border: 1px solid #eee; margin: 4px 0;" :preview-src-list="[cert.certificateImageUrl]" />
            </div>
            <div class="cert-status" :class="cert.status === 'VERIFIED' ? 'pass' : cert.status === 'PENDING_VERIFICATION' ? 'wait' : 'reject'">
              审核状态: {{ cert.status === 'VERIFIED' ? '已认证' : cert.status === 'PENDING_VERIFICATION' ? '待认证' : '已驳回' }}
            </div>
          </div>
          <div class="section-title">求职与工作</div>
          <div v-for="job in detailData.employmentList" :key="job.employmentId" class="job-block">
            <div class="job-title">[工作] {{ job.company }}</div>
            <div class="job-info">职位: {{ job.position }}　任职时间: {{ job.startDate }} ~ {{ job.endDate || '至今' }}　薪资: {{ job.salary }}元/月</div>
          </div>
          <div v-for="apply in detailData.jobApplicationList" :key="apply.applicationId" class="job-block">
            <div class="job-title">[求职] {{ apply.company }}</div>
            <div class="job-info">申请岗位: {{ apply.position }}　申请日期: {{ apply.applyDate }}　状态: {{ apply.status }}</div>
          </div>
          <div class="section-title">用户评价</div>
          <div v-for="comment in detailData.commentList" :key="comment.date + comment.role + comment.service_name" class="comment-block">
            <div class="comment-title">{{ comment.role }}: {{ comment.reviewer_name || comment.name }}<span v-if="comment.service_type === '订单'"> (订单: {{ comment.service_name }})</span><span v-else-if="comment.service_type === '课程'"> (课程: {{ comment.service_name }})</span></div>
            <div class="comment-info">服务项目: {{ comment.service || comment.service_name }}　评价时间: {{ comment.date || comment.comment_date }}　评分: <span class="star">{{ '★★★★★'.slice(0, comment.score) }}</span></div>
            <div class="comment-content">{{ comment.content }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed, nextTick } from 'vue'
import { Document } from '@element-plus/icons-vue'
import * as html2pdf from 'html2pdf.js'
import { TalentPoolApi } from '@/api/apachecore/talentpool/index'
import dayjs from 'dayjs'

const props = defineProps({
  userId: {
    type: [String, Number],
    required: true
  }
})

const detailData = ref(null)
const activeTab = ref('education')
const pdfAllTabsRef = ref(null)
const talentName = ref('')

const realNameTags = computed(() =>
  (detailData.value?.userTagList || []).filter(tag => tag.tagName === '已实名')
)

const fetchDetail = async () => {
  if (!props.userId) return
  const res = await TalentPoolApi.getTalentProfileDetail({ id: props.userId })
  // 日期字段格式化
  if (res.user) {
    res.user.birthDate = formatDate(res.user.birthDate)
  }
  if (Array.isArray(res.educationList)) {
    res.educationList.forEach(e => {
      e.startDate = formatDate(e.startDate)
      e.endDate = formatDate(e.endDate)
    })
  }
  if (Array.isArray(res.campusPracticeList)) {
    res.campusPracticeList.forEach(e => {
      e.startDate = formatDate(e.startDate)
      e.endDate = formatDate(e.endDate)
    })
  }
  if (Array.isArray(res.internshipList)) {
    res.internshipList.forEach(e => {
      e.startDate = formatDate(e.startDate)
      e.endDate = formatDate(e.endDate)
    })
  }
  if (Array.isArray(res.trainingList)) {
    res.trainingList.forEach(e => {
      e.completeDate = formatDate(e.completeDate)
    })
  }
  if (Array.isArray(res.certificateList)) {
    res.certificateList.forEach(e => {
      e.issueDate = formatDate(e.issueDate)
    })
  }
  if (Array.isArray(res.jobApplicationList)) {
    res.jobApplicationList.forEach(e => {
      e.applyDate = formatDate(e.applyDate)
    })
  }
  if (Array.isArray(res.employmentList)) {
    res.employmentList.forEach(e => {
      e.startDate = formatDate(e.startDate)
      e.endDate = formatDate(e.endDate)
    })
  }
  if (Array.isArray(res.timelineList)) {
    res.timelineList.forEach(e => {
      e.date = formatDate(e.date)
    })
  }
  detailData.value = res
  talentName.value = res?.user?.name || ''
}

function formatDate(val) {
  if (!val) return ''
  // 先去除"上午""下午"等中文
  let clean = String(val).replace(/[\u4e00-\u9fa5]+/g, '').replace(/\s+/g, ' ').trim()
  // 处理两位数年份（如25-7-8），补全为2025
  // 支持 25-7-8、25/7/8、25.7.8
  const match = clean.match(/^(\d{2})[-/.](\d{1,2})[-/.](\d{1,2})/)
  if (match) {
    const year = parseInt(match[1], 10)
    const month = match[2].padStart(2, '0')
    const day = match[3].padStart(2, '0')
    // 仅支持21世纪
    return `20${year}-${month}-${day}`
  }
  // 其它常规格式交给 dayjs 解析
  const d = dayjs(clean, ['YYYY-MM-DD', 'YYYY/M/D', 'YYYY.M.D', 'YYYY年M月D日', 'YYYY-MM-DDTHH:mm:ssZ', 'YYYY-MM-DD HH:mm:ss', 'YYYY-MM-DDTHH:mm:ss', 'YYYY-MM-DDTHH:mm:ss.SSSZ'], true)
  if (d.isValid()) return d.format('YYYY-MM-DD')
  // 尝试直接 new Date
  const d2 = new Date(clean)
  if (!isNaN(d2.getTime())) {
    return dayjs(d2).format('YYYY-MM-DD')
  }
  // 兜底返回原值
  return val
}

onMounted(fetchDetail)
watch(() => props.userId, fetchDetail)

const exportPdf = async () => {
  const footerBtn = document.querySelector('.footer-btn')
  if (footerBtn) footerBtn.style.display = 'none'
  if (pdfAllTabsRef.value) {
    pdfAllTabsRef.value.style.display = 'block'
    await nextTick()
    html2pdf.default()
      .set({
        margin: 0,
        filename: `${talentName.value}个人简历.pdf`,
        html2canvas: { scale: 2, useCORS: true },
        jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
      })
      .from(pdfAllTabsRef.value)
      .save()
      .then(() => {
        if (footerBtn) footerBtn.style.display = ''
        pdfAllTabsRef.value.style.display = 'none'
      })
  }
}
</script>

<style scoped>
.talent-detail-wrapper {
  background: #fff;
  padding: 20px 40px 24px 40px;
  min-width: 480px;
  min-height: 0;
  box-sizing: border-box;
  overflow-y: auto;
  position: relative;
  z-index: 10;
}
.divider {
  margin: 12px 0;
  border-color: #f0f0f0;
}
.header-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 16px;
}
.profile-row {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}
.avatar {
  margin-right: 32px;
  font-size: 32px;
  background: #409EFF;
  color: #fff;
}
.profile-info {
  flex: 1;
}
.profile-name {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
}
.real-tag {
  margin-left: 8px;
}
.profile-base-info {
  display: flex;
  flex-direction: column;
  gap: 6px 0;
  color: #666;
  font-size: 14px;
}
.divider-bar {
  color: #bbb;
  margin: 0 8px;
}
.section-title {
  font-size: 16px;
  font-weight: bold;
  margin: 24px 0 12px 0;
}
.tag-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 12px 16px;
  margin-bottom: 8px;
}
.tag-item {
  font-size: 13px;
  padding: 2px 12px;
  border-radius: 16px;
}
.timeline-box {
  margin-top: 8px;
  margin-bottom: 32px;
}
.footer-btn {
  display: flex;
  justify-content: flex-end;
  margin-top: 0;
  margin-bottom: 0;
}
.detail-archive-card {
  background: #fafbfc;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  padding: 16px 5px 8px 5px;
  margin: 16px 5px 24px 5px;
  border: none;
}
.archive-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 18px;
}
.archive-tabs {
  margin-bottom: 0;
}
.edu-block {
  margin-bottom: 18px;
}
.edu-school {
  font-weight: bold;
  font-size: 15px;
  margin-bottom: 4px;
}
.edu-info {
  color: #666;
  font-size: 13px;
}
.edu-divider {
  margin: 12px 0 0 0;
  border-color: #f0f0f0;
}
.practice-block, .train-block, .cert-block, .job-block, .comment-block {
  background: #fff;
  margin-bottom: 16px;
  padding: 0 0 8px 0;
}
.practice-title, .train-title, .cert-title, .job-title, .comment-title {
  font-weight: bold;
  font-size: 15px;
  margin-bottom: 2px;
}
.practice-info, .train-info, .cert-info, .job-info, .comment-info {
  color: #666;
  font-size: 13px;
  margin-bottom: 2px;
}
.practice-desc, .comment-content {
  color: #444;
  font-size: 13px;
}
.cert-status {
  font-size: 13px;
  margin-top: 2px;
  display: inline-block;
  padding: 0 8px;
  border-radius: 10px;
}
.cert-status.pass {
  color: #1abc9c;
  background: #e8f8f5;
}
.cert-status.wait {
  color: #e6a23c;
  background: #fdf6ec;
}
.cert-status.reject {
  color: #f56c6c;
  background: #fef0f0;
}
.star {
  color: #f39c12;
  font-size: 15px;
  letter-spacing: 1px;
}
.cert-image {
  margin: 4px 0 8px 0;
}
</style>
