<!--
  页面名称：新增考题
  功能描述：新增考题，支持表单校验、提交、重置，题型切换动态渲染选项和答案
-->
<template>
  <el-drawer v-model="visible" title="新增考题" size="700px" direction="rtl" :close-on-click-modal="false" @close="onCancel">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="100px" class="add-question-form">
      <div class="section-title">基础信息</div>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="一级名称" prop="level1">
            <el-select v-model="form.level1" placeholder="请选择一级名称">
              <el-option v-for="item in level1Options" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="一级代码" prop="level1Code">
            <el-input v-model="form.level1Code" placeholder="请输入一级代码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="二级名称" prop="level2">
            <el-select v-model="form.level2" placeholder="请选择二级名称">
              <el-option v-for="item in level2Options" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="二级代码" prop="level2Code">
            <el-input v-model="form.level2Code" placeholder="请输入二级代码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="三级名称" prop="level3">
            <el-select v-model="form.level3" placeholder="请选择三级名称">
              <el-option v-for="item in level3Options" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="三级代码" prop="level3Code">
            <el-input v-model="form.level3Code" placeholder="请输入三级代码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="认定点名称" prop="certName">
            <el-input v-model="form.certName" placeholder="知识点名称，如：职业道德基础" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="认定点代码" prop="certCode">
            <el-input v-model="form.certCode" placeholder="如：KP001" />
          </el-form-item>
        </el-col>
      </el-row>
      <div class="section-title">题目内容</div>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="题型" prop="type">
            <el-select v-model="form.type" placeholder="请选择题型">
              <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="题干" prop="title">
            <el-input v-model="form.title" type="textarea" :rows="3" placeholder="请输入题目内容..." />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 动态渲染选择项和参考答案 -->
      <template v-if="['单选题','多选题','判断题','排序题','匹配题'].includes(form.type)">
        <div class="section-title">选择项 <span v-if="form.type==='排序题'">（请填写需要排序的选项）</span></div>
        <div v-if="form.type==='匹配题'">
          <div style="font-weight:bold;">左列选项：</div>
          <el-input v-for="(item, idx) in matchLeft" :key="'left'+idx" v-model="matchLeft[idx]" placeholder="请输入左列选项{{idx+1}}" style="margin-bottom:8px;" />
          <el-button size="small" @click="addMatchLeft">+ 添加选项</el-button>
          <div style="font-weight:bold;margin-top:12px;">右列选项：</div>
          <el-input v-for="(item, idx) in matchRight" :key="'right'+idx" v-model="matchRight[idx]" placeholder="请输入右列选项{{String.fromCharCode(65+idx)}}" style="margin-bottom:8px;" />
          <el-button size="small" @click="addMatchRight">+ 添加选项</el-button>
        </div>
        <div v-else>
          <el-input v-for="(item, idx) in options" :key="idx" v-model="options[idx]" :placeholder="optionPlaceholder(idx)" style="margin-bottom:8px;" />
          <el-button size="small" @click="addOption">+ 添加选项</el-button>
        </div>
      </template>
      <!-- 填空题 -->
      <template v-if="form.type==='填空题'">
        <div class="section-title">参考答案</div>
        <el-form-item label="参考答案" prop="answer">
          <el-input v-model="form.answer" type="textarea" :rows="2" placeholder="请输入填空答案，多个空用'|'分隔，如：答案1|答案2" />
          <div class="form-tip">填空题请填写标准答案，多个空用'|'分隔，如：答案1|答案2</div>
        </el-form-item>
      </template>
      <!-- 材料题、文件上传题、简答题 -->
      <template v-if="['简答题','材料题','文件上传题'].includes(form.type)">
        <div class="section-title">参考答案</div>
        <el-form-item label="参考答案" prop="answer">
          <el-input v-model="form.answer" type="textarea" :rows="3" :placeholder="answerPlaceholder" />
          <div class="form-tip">{{ answerTip }}</div>
        </el-form-item>
      </template>
      <!-- 参考答案区（选择题/判断/排序/匹配） -->
      <template v-if="['单选题','多选题','判断题','排序题','匹配题'].includes(form.type)">
        <div class="section-title">参考答案</div>
        <el-form-item label="参考答案" prop="answer">
          <el-input v-model="form.answer" type="textarea" :rows="2" :placeholder="answerPlaceholder" />
          <div class="form-tip">{{ answerTip }}</div>
        </el-form-item>
      </template>
      <div class="dialog-footer">
        <el-button @click="onCancel">取消</el-button>
        <el-button type="primary" @click="onSubmit">保存</el-button>
      </div>
    </el-form>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
const props = defineProps({
  formData: {
    type: Object,
    default: () => ({})
  }
})

/** 表单显示控制 */
const visible = defineModel<boolean>('visible', { default: false })

/** 表单数据 */
const form = reactive({
  level1: '',
  level1Code: '',
  level2: '',
  level2Code: '',
  level3: '',
  level3Code: '',
  certName: '',
  certCode: '',
  type: '',
  title: '',
  answer: ''
})

// 编辑时回显数据
watch(
  () => props.formData,
  (val) => {
    if (visible.value && val) {
      Object.assign(form, val)
    }
  },
  { immediate: true, deep: true }
)

/** 下拉选项 */
const level1Options = [
  { label: '职业技能等级认定', value: '职业技能等级认定' },
  { label: '专项职业能力考核', value: '专项职业能力考核' },
  { label: '职业资格认证', value: '职业资格认证' }
]
const level2Options = [
  { label: '家政服务类', value: '家政服务类' },
  { label: '技术工人类', value: '技术工人类' },
  { label: '专业技术类', value: '专业技术类' },
  { label: '管理人员类', value: '管理人员类' }
]
const level3Options = [
  { label: '家政服务员', value: '家政服务员' },
  { label: '育婴员', value: '育婴员' },
  { label: '电工', value: '电工' },
  { label: '健康管理师', value: '健康管理师' },
  { label: '母婴护理', value: '母婴护理' },
  { label: '焊工', value: '焊工' },
  { label: '美容师', value: '美容师' },
  { label: '车工', value: '车工' }
]
const typeOptions = [
  { label: '单选题', value: '单选题' },
  { label: '多选题', value: '多选题' },
  { label: '判断题', value: '判断题' },
  { label: '简答题', value: '简答题' },
  { label: '填空题', value: '填空题' },
  { label: '材料题', value: '材料题' },
  { label: '排序题', value: '排序题' },
  { label: '匹配题', value: '匹配题' },
  { label: '文件上传题', value: '文件上传题' }
]
const options = ref<string[]>(['', '', '', ''])
const matchLeft = ref<string[]>([''])
const matchRight = ref<string[]>([''])

/** 动态渲染选项和答案 */
watch(() => form.type, (val) => {
  if(['单选题','多选题','排序题'].includes(val)) {
    options.value = ['', '', '', '']
  } else if(val === '判断题') {
    options.value = ['正确', '错误']
  } else if(val === '匹配题') {
    matchLeft.value = ['']
    matchRight.value = ['']
  }
  form.answer = ''
})

/** 添加选项 */
function addOption() {
  options.value.push('')
}
function addMatchLeft() {
  matchLeft.value.push('')
}
function addMatchRight() {
  matchRight.value.push('')
}
function optionPlaceholder(idx:number) {
  if(form.type==='排序题') return `请输入需要排序的选项${String.fromCharCode(65+idx)}`
  return `请输入选项${String.fromCharCode(65+idx)}`
}

/** 参考答案占位符 */
const answerPlaceholder = computed(() => {
  switch(form.type) {
    case '单选题': return '请输入正确答案(如:A)'
    case '多选题': return '请输入正确答案(如:A,C)'
    case '判断题': return '请输入正确答案(正确/错误)'
    case '排序题': return '请输入正确排序，如：C,A,B,D'
    case '匹配题': return '请输入匹配关系，如：1-A,2-B,3-C'
    case '填空题': return '请输入填空答案，多个空用|分隔，如：答案1|答案2'
    case '简答题': return '请输入参考答案'
    case '材料题': return '请输入参考答案'
    case '文件上传题': return '请输入评分标准和文件要求'
    default: return ''
  }
})

/** 参考答案提示 */
const answerTip = computed(() => {
  switch(form.type) {
    case '单选题': return '单选题请填写选项字母，如：A'
    case '多选题': return '多选题请填写多个选项字母，用逗号分隔，如：A,C'
    case '判断题': return '判断题请填写：正确 或 错误'
    case '排序题': return '排序题请填写正确的排序顺序，用逗号分隔，如：C,A,B,D'
    case '匹配题': return '匹配题请填写正确的匹配关系，如：1-A,2-B,3-C'
    case '填空题': return '填空题请填写标准答案，多个空用|分隔，如：答案1|答案2'
    case '简答题': return '简答题请填写详细的参考答案'
    case '材料题': return '材料题请填写详细的参考答案'
    case '文件上传题': return '文件上传请详细说明评分标准、文件格式要求、大小限制等'
    default: return ''
  }
})

/** 校验规则 */
const rules = {
  level1: [{ required: true, message: '请选择一级名称', trigger: 'change' }],
  level1Code: [{ required: true, message: '请输入一级代码', trigger: 'blur' }],
  level2: [{ required: true, message: '请选择二级名称', trigger: 'change' }],
  level2Code: [{ required: true, message: '请输入二级代码', trigger: 'blur' }],
  level3: [{ required: true, message: '请选择三级名称', trigger: 'change' }],
  level3Code: [{ required: true, message: '请输入三级代码', trigger: 'blur' }],
  certName: [{ required: true, message: '请输入认定点名称', trigger: 'blur' }],
  certCode: [{ required: true, message: '请输入认定点代码', trigger: 'blur' }],
  type: [{ required: true, message: '请选择题型', trigger: 'change' }],
  title: [{ required: true, message: '请输入题目内容', trigger: 'blur' }],
  answer: [{ required: true, message: '请输入参考答案', trigger: 'blur' }]
}

const formRef = ref()

/** 提交表单 */
const onSubmit = () => {
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      // TODO: 提交逻辑
      visible.value = false
    }
  })
}

/** 取消/关闭弹窗 */
const onCancel = () => {
  visible.value = false
}
</script>

<style scoped lang="scss">
.add-question-form {
  padding-bottom: 0;
}
.section-title {
  font-weight: bold;
  font-size: 16px;
  margin: 16px 0 8px 0;
}
.form-tip {
  font-size: 12px;
  color: #888;
  margin-top: 4px;
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  gap: 8px;
}
</style>
