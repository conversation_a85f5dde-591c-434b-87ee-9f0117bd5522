package cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 考题管理分页查询 Request VO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "考题管理分页查询 Request VO")
public class QuestionPageReqVO extends PageParam {

    @Schema(description = "一级分类名称", example = "职业技能等级认定")
    private String level1Name;

    @Schema(description = "二级分类名称", example = "家政服务类")
    private String level2Name;

    @Schema(description = "三级分类名称", example = "家政服务员")
    private String level3Name;

    @Schema(description = "认定点名称", example = "职业道德基础")
    private String certName;

    @Schema(description = "题型", example = "单选题")
    private String type;

    @Schema(description = "业务模块", example = "家政业务")
    private String biz;

    @Schema(description = "关键词搜索（题干内容）", example = "职业道德")
    private String keyword;

    @Schema(description = "状态：0-禁用，1-启用", example = "1")
    private Integer status;

    @Schema(description = "难度等级：1-简单，2-中等，3-困难", example = "1")
    private Integer difficulty;
}
