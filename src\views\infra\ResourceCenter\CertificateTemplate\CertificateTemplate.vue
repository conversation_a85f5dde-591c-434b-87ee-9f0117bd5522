<template>
  <div class="certificate-template-page">
    <!-- 顶部统计卡片 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :xs="24" :sm="12" :md="6" v-for="card in stats" :key="card.label">
        <el-card :class="['stat-card', card.bgClass]">
          <div class="stat-card-inner">
            <el-icon :class="['stat-icon', card.iconClass]">
              <component :is="card.icon" />
            </el-icon>
            <div>
              <div class="stat-num">{{ card.value }}</div>
              <div class="stat-label">{{ card.label }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <!-- 筛选区 -->
    <el-row :gutter="12" class="filter-row" align="middle" style="margin: 24px 0 12px 0;">
      <el-col :span="4" :xs="24" :sm="6">
        <el-select v-model="filter.type" placeholder="全部类型" clearable style="width: 100%">
          <el-option label="全部类型" value="" />
          <el-option label="培训证书" value="培训证书" />
          <el-option label="技能等级证书" value="技能等级证书" />
          <el-option label="结业证书" value="结业证书" />
        </el-select>
      </el-col>
      <el-col :span="4" :xs="24" :sm="6">
        <el-select v-model="filter.status" placeholder="全部状态" clearable style="width: 100%">
          <el-option label="全部状态" value="" />
          <el-option label="启用中" value="启用中" />
          <el-option label="已停用" value="已停用" />
          <el-option label="草稿" value="草稿" />
        </el-select>
      </el-col>
      <el-col :span="6" :xs="24" :sm="8">
        <el-input v-model="filter.keyword" placeholder="搜索模板名称..." clearable />
      </el-col>
      <el-col :span="2" :xs="12" :sm="2">
        <el-button type="primary" @click="onFilter" style="width: 100%">筛选</el-button>
      </el-col>
 
    </el-row>
    <!-- 数据表格 -->
    <el-table :data="filteredList" border stripe class="template-table" style="width: 100%">
      <el-table-column prop="name" label="模板名称" min-width="160" />
      <el-table-column prop="type" label="证书类型" min-width="100" />
      <el-table-column prop="course" label="适用课程" min-width="140" />
      <el-table-column prop="creator" label="创建人" min-width="80" />
      <el-table-column prop="status" label="状态" min-width="80">
        <template #default="scope">
          <el-tag :type="scope.row.status === '启用中' ? 'success' : (scope.row.status === '草稿' ? 'warning' : 'info')">
            {{ scope.row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createdAt" label="创建时间" min-width="120" />
      <el-table-column label="操作" min-width="180">
        <template #default="scope">
          <el-button size="small" @click="onEdit(scope.row)">编辑</el-button>
          <el-button size="small" @click="onPreview(scope.row)">预览</el-button>
          <el-button
            v-if="scope.row.status === '启用中'"
            size="small"
            type="danger"
            @click="onDisable(scope.row)"
          >停用</el-button>
          <el-button
            v-else
            size="small"
            type="primary"
            @click="onEnable(scope.row)"
          >启用</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 编辑证书模板抽屉 -->
    <AddCertificateTemplate v-model:visible="editDrawerVisible" />
    <!-- 预览证书抽屉 -->
    <PreviewCertificate v-model:visible="previewDrawerVisible" :data="currentPreviewRow" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Edit, View, Plus, Check, Close, Document, EditPen, Tickets } from '@element-plus/icons-vue'
import AddCertificateTemplate from './components/AddCertificateTemplate.vue'
import PreviewCertificate from './components/PreviewCertificate.vue'

// 统计卡片 mock 数据
const stats = [
  { label: '证书模板总数', value: 3, icon: 'Tickets', iconClass: 'stat-icon1', bgClass: 'stat-bg1' },
  { label: '启用中', value: 2, icon: 'Check', iconClass: 'stat-icon2', bgClass: 'stat-bg2' },
  { label: '已停用', value: 0, icon: 'Close', iconClass: 'stat-icon3', bgClass: 'stat-bg3' },
  { label: '草稿状态', value: 1, icon: 'EditPen', iconClass: 'stat-icon4', bgClass: 'stat-bg4' },
]

// 表格 mock 数据
const tableData = ref([
  {
    name: '新媒体初级培训证书模板',
    type: '培训证书',
    course: '新媒体运营基础',
    creator: '张三',
    status: '启用中',
    createdAt: '2024-08-01',
  },
  {
    name: 'UI/UX设计技能等级证书',
    type: '技能等级证书',
    course: 'UI/UX设计进阶',
    creator: '李四',
    status: '启用中',
    createdAt: '2024-07-25',
  },
  {
    name: 'Python编程结业证书',
    type: '结业证书',
    course: 'Python数据分析实战',
    creator: '王五',
    status: '草稿',
    createdAt: '2024-07-20',
  },
])

// 筛选条件
const filter = ref({ type: '', status: '', keyword: '' })

const filteredList = computed(() => {
  return tableData.value.filter(item => {
    const typeOk = !filter.value.type || item.type === filter.value.type
    const statusOk = !filter.value.status || item.status === filter.value.status
    const keywordOk = !filter.value.keyword || item.name.includes(filter.value.keyword)
    return typeOk && statusOk && keywordOk
  })
})

// 操作区方法（mock）
function onFilter() {}
function onAdd() { /* 打开新建模板弹窗 */ }
function onEdit(row: any) {
  currentEditRow.value = row
  editDrawerVisible.value = true
}
function onPreview(row: any) {
  currentPreviewRow.value = row
  previewDrawerVisible.value = true
}
function onEnable(row: any) { row.status = '启用中' }
function onDisable(row: any) { row.status = '已停用' }

// 编辑抽屉显示控制
const editDrawerVisible = ref(false)
// 当前编辑行数据（预留，后续可传递给AddCertificateTemplate）
const currentEditRow = ref<any>(null)

// 预览抽屉显示控制
const previewDrawerVisible = ref(false)
const currentPreviewRow = ref<any>(null)
</script>

<style scoped lang="scss">
.certificate-template-page {
  padding: 24px;
  background: #fff;
}
.stats-row {
  margin-bottom: 16px;
}
.stat-card {
  border: none;
  box-shadow: 0 2px 8px #f0f1f2;
  border-radius: 8px;
  padding: 0;
}
.stat-card-inner {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 0 8px 8px;
}
.stat-icon {
  font-size: 24px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255,255,255,0.3);
}
.stat-icon1 {
  color: #409EFF;
  background: #e6f1ff;
}
.stat-icon2 {
  color: #67C23A;
  background: #e8f7e0;
}
.stat-icon3 {
  color: #E6A23C;
  background: #fff5e6;
}
.stat-icon4 {
  color: #F56C6C;
  background: #ffeaea;
}
.stat-bg1 {
  background: linear-gradient(90deg, #e6f1ff 0%, #cce3ff 100%);
}
.stat-bg2 {
  background: linear-gradient(90deg, #e8f7e0 0%, #d2f2c2 100%);
}
.stat-bg3 {
  background: linear-gradient(90deg, #fff5e6 0%, #ffe1b8 100%);
}
.stat-bg4 {
  background: linear-gradient(90deg, #ffeaea 0%, #ffd6d6 100%);
}
.stat-num {
  font-size: 18px;
  font-weight: bold;
}
.stat-label {
  color: #888;
  font-size: 12px;
}
.mb-4 {
  margin-bottom: 24px;
}
.filter-row {
  margin-bottom: 8px;
}
.template-table {
  margin-top: 8px;
}
@media (max-width: 768px) {
  .certificate-template-page {
    padding: 8px;
  }
  .stat-card {
    flex-direction: column;
    align-items: flex-start;
    .stat-icon {
      margin-bottom: 8px;
      margin-right: 0;
    }
  }
  .filter-row {
    flex-wrap: wrap;
    .el-col {
      margin-bottom: 8px;
    }
  }
}
</style>
