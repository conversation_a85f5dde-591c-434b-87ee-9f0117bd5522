<template>
  <el-dialog
    v-model="visible"
    title="证书预览"
    width="700px"
    top="40px"
    :close-on-click-modal="false"
    :show-close="true"
    class="preview-certificate-dialog"
  >
    <div class="preview-certificate-main">
      <div class="certificate-card">
        <div class="card-title">{{ data.name || '新媒体初级培训证书模板' }}</div>
        <div class="card-content">
          <div class="student-name">张三</div>
          <div class="certificate-title">新媒体初级培训证书</div>
          <div class="certificate-desc">兹证明学员已完成相关培训课程</div>
          <div class="issue-date">
            <span>发证日期：</span><b>2024年8月1日</b>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
const props = defineProps<{ visible: boolean, data?: any }>()
const emit = defineEmits(['update:visible'])
const visible = ref(props.visible)
watch(() => props.visible, v => visible.value = v)
watch(visible, v => emit('update:visible', v))
const handleClose = () => { visible.value = false }
</script>

<style scoped lang="scss">
.preview-certificate-dialog {
  .el-dialog__body {
    padding: 0 32px 24px 32px;
    background: #f8f9fa;
  }
}
.preview-certificate-main {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 480px;
}
.certificate-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 12px #e6e7e9;
  padding: 32px 32px 32px 32px;
  width: 520px;
  min-height: 380px;
  margin: 0 auto;
  position: relative;
}
.card-title {
  text-align: center;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 24px;
}
.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 260px;
  position: relative;
}
.student-name {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 24px;
  margin-top: 12px;
}
.certificate-title {
  font-size: 22px;
  font-weight: bold;
  margin-bottom: 8px;
}
.certificate-desc {
  font-size: 16px;
  color: #666;
  margin-bottom: 24px;
}
.issue-date {
  font-size: 16px;
  color: #222;
  margin-bottom: 12px;
  font-weight: bold;
}
.qrcode-field {
  position: absolute;
  right: 0;
  bottom: 0;
  font-size: 15px;
  color: #333;
  border: 1px dashed #bbb;
  border-radius: 4px;
  padding: 4px 10px;
  background: #f8f9fa;
}
</style>
