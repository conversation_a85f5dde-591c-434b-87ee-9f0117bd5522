<template>
  <el-row :gutter="20">
    <!-- 左侧分类树 -->
    <el-col :span="4" :xs="24">
      <ContentWrap class="h-1/1">
        <!-- 分类树组件，后续可独立抽离 -->
        <div class="head-container" style="display: flex; align-items: center">
          <el-input
            v-model="categoryKeyword"
            class="mb-20px"
            clearable
            placeholder="请输入分类关键字"
          >
            <template #prefix>
              <Icon icon="ep:search" />
            </template>
          </el-input>
        </div>
        <div class="head-container">
          <el-tree
            ref="categoryTreeRef"
            :data="categoryList"
            :expand-on-click-node="false"
            :filter-node-method="filterCategoryNode"
            :props="categoryTreeProps"
            default-expand-all
            highlight-current
            node-key="id"
            @node-click="handleCategoryNodeClick"
          >
            <template #default="{ node, data }">
              <div class="category-node">
                <span class="category-name">{{ node.label }}</span>
                <div class="category-actions" v-if="!data.children || data.children.length === 0">
                  <el-button
                    type="text"
                    size="small"
                    @click.stop="handleEditCategory(data)"
                    style="color: #409eff; margin-right: 8px"
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="text"
                    size="small"
                    @click.stop="handleDeleteCategory(data)"
                    style="color: #f56c6c"
                  >
                    删除
                  </el-button>
                </div>
              </div>
            </template>
          </el-tree>
        </div>
        <el-button type="primary" class="mt-10px w-full" @click="openCategoryDialog"
          >+ 新增分类</el-button
        >
        <el-button
          icon="el-icon-refresh"
          class="w-full"
          style="margin-top: 8px; height: 32px; margin-left: 0px"
          @click="handleCategoryReset"
        >
          <el-icon style="vertical-align: middle; margin-right: 4px">
            <Refresh />
          </el-icon>
          重置查询
        </el-button>
      </ContentWrap>
    </el-col>
    <el-col :span="20" :xs="24">
      <ContentWrap>
        <!-- Tab 切换 -->
        <el-tabs v-model="activeTab">
          <el-tab-pane label="图片" name="image" />
          <el-tab-pane label="视频" name="video" />
          <el-tab-pane label="音频" name="voice" />
          <el-tab-pane label="文章" name="article" />
          <el-tab-pane label="文档" name="doc" />
          <el-tab-pane label="图文" name="graphic" />
          <el-tab-pane label="回收站" name="recycle" />
        </el-tabs>
        <!-- 仅图片 tab 展示内容 -->
        <div v-if="activeTab === 'image'">
          <!-- 搜索区 -->
          <div class="search-bar">
            <el-input
              v-model="imageSearchForm.name"
              placeholder="素材名称"
              clearable
              style="width: 200px; margin-right: 16px"
            />
            <el-select
              v-model="imageSearchForm.visibleOrgId"
              placeholder="请选择来源"
              style="width: 160px; margin-right: 16px"
              clearable
              @visible-change="handleImageSourceDropdown"
            >
              <el-option label="请选择" value="" />
              <el-option
                v-for="item in imageSourceOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-button type="primary" @click="handleImageSearch">查询</el-button>
            <el-button @click="handleImageReset">重置</el-button>
            <el-button
              type="primary"
              style="float: right"
              class="ml-auto"
              @click="handleAddMaterial"
              >+ 新增图片</el-button
            >
          </div>
          <!-- 表格区 -->
          <el-table :data="imageList" style="width: 100%; margin-top: 20px">
            <el-table-column label="图片内容" width="100">
              <template #default="{ row }">
                <img
                  v-if="row.url"
                  :src="row.url"
                  alt="图片"
                  style="width: 60px; height: 60px; object-fit: cover"
                />
                <span v-else>无</span>
              </template>
            </el-table-column>
            <el-table-column prop="id" label="图片ID" />
            <el-table-column prop="name" label="图片名称" />
            <el-table-column prop="source" label="素材来源">
              <template #default="{ row }">
                <span>{{ row.visibleOrgName || '未知' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间">
              <template #default="{ row }">
                <span>{{
                  row.createTime ? dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '-'
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="100">
              <template #default="{ row }">
                <el-button type="text" size="small" @click="handleEditPicture(row)">编辑</el-button>
                <el-button
                  type="text"
                  size="small"
                  style="color: #f56c6c"
                  @click="handleDeletePicture(row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页区 -->
          <Pagination
            :total="imageTotal"
            v-model:page="imagePage"
            v-model:limit="imagePageSize"
            @pagination="handleImagePageChange"
          />
        </div>
        <!-- 视频 tab 展示内容 -->
        <div v-if="activeTab === 'video'">
          <!-- 搜索区 -->
          <div class="search-bar">
            <el-input
              v-model="videoSearchForm.name"
              placeholder="素材名称"
              clearable
              style="width: 200px; margin-right: 16px"
            />
            <el-select
              v-model="videoSearchForm.visibleOrgId"
              placeholder="请选择来源"
              style="width: 160px; margin-right: 16px"
              clearable
              @visible-change="handleVideoSourceDropdown"
            >
              <el-option label="请选择" value="" />
              <el-option
                v-for="item in videoSourceOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-button type="primary" @click="handleVideoSearch">查询</el-button>
            <el-button @click="handleVideoReset">重置</el-button>
            <el-button type="primary" style="float: right" class="ml-auto" @click="handleAddVideo"
              >+ 新增视频</el-button
            >
          </div>
          <!-- 表格区 -->
          <el-table :data="videoList" style="width: 100%; margin-top: 20px">
            <el-table-column label="视频内容" width="100">
              <template #default="{ row }">
                <video
                  v-if="row.videoUrl || row.url"
                  :src="row.videoUrl || row.url"
                  controls
                  style="width: 80px; height: 60px; object-fit: cover"
                ></video>
                <span v-else>无</span>
              </template>
            </el-table-column>
            <el-table-column prop="id" label="视频ID" />
            <el-table-column prop="name" label="视频名称" />
            <el-table-column prop="visibleOrgName" label="素材来源">
              <template #default="{ row }">
                <span>{{ row.visibleOrgName || '未知' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间">
              <template #default="{ row }">
                <span>{{
                  row.createTime ? dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '-'
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="100">
              <template #default="{ row }">
                <el-button type="text" size="small" @click="handleEditVideo(row)">编辑</el-button>
                <el-button
                  type="text"
                  size="small"
                  style="color: #f56c6c"
                  @click="handleDeleteVideo(row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页区 -->
          <Pagination
            :total="videoTotal"
            v-model:page="videoPage"
            v-model:limit="videoPageSize"
            @pagination="handleVideoPageChange"
          />
        </div>
        <!-- 音频 tab 展示内容 -->
        <div v-if="activeTab === 'voice'">
          <!-- 搜索区 -->
          <div class="search-bar">
            <el-input
              v-model="voiceSearchForm.name"
              placeholder="素材名称"
              clearable
              style="width: 200px; margin-right: 16px"
            />
            <el-select
              v-model="voiceSearchForm.visibleOrgId"
              placeholder="请选择来源"
              style="width: 160px; margin-right: 16px"
              clearable
              @visible-change="handleVoiceSourceDropdown"
            >
              <el-option label="请选择" value="" />
              <el-option
                v-for="item in voiceSourceOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-button type="primary" @click="handleVoiceSearch">查询</el-button>
            <el-button @click="handleVoiceReset">重置</el-button>
            <el-button type="primary" style="float: right" class="ml-auto" @click="handleAddVoice"
              >+ 新增音频</el-button
            >
          </div>
          <!-- 表格区 -->
          <el-table :data="voiceList" style="width: 100%; margin-top: 20px">
            <el-table-column label="音频内容" width="100">
              <template #default="{ row }">
                <div v-if="row.audioUrl || row.url" class="audio-cell">
                  <audio
                    :src="row.audioUrl || row.url"
                    controls
                    style="width: 80px; height: 40px"
                  ></audio>
                </div>
                <span v-else>无</span>
              </template>
            </el-table-column>
            <el-table-column prop="id" label="音频ID" />
            <el-table-column prop="name" label="音频名称" />
            <el-table-column prop="visibleOrgName" label="素材来源">
              <template #default="{ row }">
                <span>{{ row.visibleOrgName || '未知' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="duration" label="时长">
              <template #default="{ row }">
                <span v-if="row.duration">{{ formatDuration(row.duration) }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间">
              <template #default="{ row }">
                <span>{{
                  row.createTime ? dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '-'
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="100">
              <template #default="{ row }">
                <el-button type="text" size="small" @click="handleEditVoice(row)">编辑</el-button>
                <el-button
                  type="text"
                  size="small"
                  style="color: #f56c6c"
                  @click="handleDeleteVoice(row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页区 -->
          <Pagination
            :total="voiceTotal"
            v-model:page="voicePage"
            v-model:limit="voicePageSize"
            @pagination="handleVoicePageChange"
          />
        </div>
        <!-- 文章 tab 展示内容 -->
        <div v-if="activeTab === 'article'">
          <!-- 搜索区 -->
          <div class="search-bar">
            <el-input
              v-model="articleSearchForm.title"
              placeholder="文章标题"
              clearable
              style="width: 200px; margin-right: 16px"
            />
            <el-select
              v-model="articleSearchForm.visibleOrgId"
              placeholder="请选择来源"
              style="width: 160px; margin-right: 16px"
              clearable
              @visible-change="handleArticleSourceDropdown"
            >
              <el-option label="请选择" value="" />
              <el-option
                v-for="item in articleSourceOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-button type="primary" @click="handleArticleSearch">查询</el-button>
            <el-button @click="handleArticleReset">重置</el-button>
            <el-button type="primary" style="float: right" class="ml-auto" @click="handleAddArticle"
              >+ 新增文章</el-button
            >
          </div>
          <!-- 表格区 -->
          <el-table :data="articleList" style="width: 100%; margin-top: 20px">
            <el-table-column prop="title" label="文章标题" />
            <el-table-column prop="id" label="文章ID" />
            <el-table-column prop="visibleOrgName" label="素材来源">
              <template #default="{ row }">
                <span>{{ row.visibleOrgName || '未知' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间">
              <template #default="{ row }">
                <span>{{
                  row.createTime ? dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '-'
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="100">
              <template #default="{ row }">
                <el-button type="text" size="small" @click="handleEditArticle(row)">编辑</el-button>
                <el-button
                  type="text"
                  size="small"
                  style="color: #f56c6c"
                  @click="handleDeleteArticle(row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页区 -->
          <Pagination
            :total="articleTotal"
            v-model:page="articlePage"
            v-model:limit="articlePageSize"
            @pagination="handleArticlePageChange"
          />
        </div>
        <!-- 文档 tab 展示内容 -->
        <div v-if="activeTab === 'doc'">
          <!-- 搜索区 -->
          <div class="search-bar">
            <el-input
              v-model="documentSearchForm.name"
              placeholder="素材名称"
              clearable
              style="width: 200px; margin-right: 16px"
            />
            <el-select
              v-model="documentSearchForm.visibleOrgId"
              placeholder="请选择来源"
              style="width: 160px; margin-right: 16px"
              clearable
              @visible-change="handleDocumentSourceDropdown"
            >
              <el-option label="请选择" value="" />
              <el-option
                v-for="item in documentSourceOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-button type="primary" @click="handleDocumentSearch">查询</el-button>
            <el-button @click="handleDocumentReset">重置</el-button>
            <el-button type="primary" style="float: right" class="ml-auto" @click="handleAddDoc"
              >+ 新增文档</el-button
            >
          </div>
          <!-- 表格区 -->
          <el-table :data="documentList" style="width: 100%; margin-top: 20px">
            <el-table-column prop="documentName" label="文档名称">
              <template #default="{ row }">
                <span>{{ row.documentName || row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="id" label="文档ID" />
            <el-table-column prop="visibleOrgName" label="素材来源">
              <template #default="{ row }">
                <span>{{ row.visibleOrgName || '未知' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间">
              <template #default="{ row }">
                <span>{{
                  row.createTime ? dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '-'
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="100">
              <template #default="{ row }">
                <el-button type="text" size="small" @click="handleEditDoc(row)">编辑</el-button>
                <el-button
                  type="text"
                  size="small"
                  style="color: #f56c6c"
                  @click="handleDeleteDocument(row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页区 -->
          <Pagination
            :total="documentTotal"
            v-model:page="documentPage"
            v-model:limit="documentPageSize"
            @pagination="handleDocumentPageChange"
          />
        </div>
        <!-- 图文 tab 展示内容 -->
        <div v-if="activeTab === 'graphic'">
          <!-- 搜索区 -->
          <div class="search-bar">
            <el-input
              v-model="newsSearchForm.title"
              placeholder="素材名称"
              clearable
              style="width: 200px; margin-right: 16px"
            />
            <el-select
              v-model="newsSearchForm.visibleOrgId"
              placeholder="请选择来源"
              style="width: 160px; margin-right: 16px"
              clearable
              @visible-change="handleNewsSourceDropdown"
            >
              <el-option label="请选择" value="" />
              <el-option
                v-for="item in newsSourceOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-button type="primary" @click="handleNewsSearch">查询</el-button>
            <el-button @click="handleNewsReset">重置</el-button>
            <el-button type="primary" style="float: right" class="ml-auto" @click="handleAddGraphic"
              >+ 新增图文</el-button
            >
          </div>
          <!-- 表格区 -->
          <el-table :data="newsList" style="width: 100%; margin-top: 20px">
            <el-table-column label="图文内容" width="100">
              <template #default="{ row }">
                <img
                  v-if="row.thumbUrl || row.imgUrl || row.url"
                  :src="row.thumbUrl || row.imgUrl || row.url"
                  alt="图文"
                  style="width: 60px; height: 60px; object-fit: cover"
                />
                <span v-else>无</span>
              </template>
            </el-table-column>
            <el-table-column prop="id" label="图文ID" />
            <el-table-column prop="name" label="图文名称" />
            <el-table-column prop="visibleOrgName" label="素材来源">
              <template #default="{ row }">
                <span>{{ row.visibleOrgName || '未知' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间">
              <template #default="{ row }">
                <span>{{
                  row.createTime ? dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '-'
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="100">
              <template #default="{ row }">
                <el-button type="text" size="small" @click="handleEditGraphic(row)">编辑</el-button>
                <el-button
                  type="text"
                  size="small"
                  style="color: #f56c6c"
                  @click="handleDeleteNews(row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页区 -->
          <Pagination
            :total="newsTotal"
            v-model:page="newsPage"
            v-model:limit="newsPageSize"
            @pagination="handleNewsPageChange"
          />
        </div>
        <!-- 回收站 tab 展示内容 -->
        <div v-if="activeTab === 'recycle'">
          <RecycleBin />
        </div>
      </ContentWrap>
    </el-col>
  </el-row>

  <!-- 新增分类弹窗 -->
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :close-on-click-modal="false"
  >
    <el-form :model="categoryForm" :rules="categoryRules" ref="categoryFormRef" label-width="80px">
      <el-form-item label="分类名称" prop="name" required>
        <el-input v-model="categoryForm.name" placeholder="请输入分类名称" />
      </el-form-item>
      <el-form-item label="可见范围" prop="visibleOrgId" required>
        <div class="dept-tree-container">
          <el-input
            v-model="deptSearchKeyword"
            placeholder="请输入部门名称搜索"
            clearable
            style="margin-bottom: 10px"
          >
            <template #prefix>
              <Icon icon="ep:search" />
            </template>
          </el-input>
          <div class="dept-tree-wrapper">
            <el-tree
              ref="deptTreeRef"
              :data="deptList"
              :expand-on-click-node="false"
              :filter-node-method="filterDeptNode"
              :props="categoryTreeProps"
              default-expand-all
              highlight-current
              node-key="id"
              @node-click="handleDeptNodeClick"
              :filter-text="deptSearchKeyword"
              style="
                max-height: 200px;
                overflow-y: auto;
                border: 1px solid #dcdfe6;
                border-radius: 4px;
                padding: 8px;
              "
            >
              <template #default="{ node, data }">
                <span :class="{ 'selected-dept': selectedDept && selectedDept.id === data.id }">
                  {{ node.label }}
                </span>
              </template>
            </el-tree>
          </div>
          <div v-if="selectedDept" class="selected-dept-info">
            <el-tag type="primary" closable @close="handleDeptTagClose">
              已选择: {{ selectedDept.name }}
            </el-tag>
          </div>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleCategorySave">保存</el-button>
    </template>
  </el-dialog>

  <AddPicture ref="addPictureRef" @refresh="loadImageList" />
  <AddVideo ref="addVideoRef" @refresh="loadVideoList" />
  <AddVoice ref="addVoiceRef" @refresh="loadVoiceList" />
  <AddArticle ref="addArticleRef" @refresh="loadArticleList" />
  <AddDocument ref="addDocumentRef" @refresh="loadDocumentList" />
  <AddImageText ref="addImageTextRef" @refresh="loadNewsList" />
</template>

<script setup lang="ts">
import { ref, computed, watchEffect, watch } from 'vue'
import ContentWrap from '@/components/ContentWrap/src/ContentWrap.vue'
import Pagination from '@/components/Pagination/index.vue'
import { ElTree } from 'element-plus'
import { Icon } from '@/components/Icon'
import AddPicture from './components/AddPicture.vue'
import AddVideo from './components/AddVideo.vue'
import AddVoice from './components/AddVoice.vue'
import AddArticle from './components/AddArticle.vue'
import AddDocument from './components/AddDocument.vue'
import AddImageText from './components/AddImageText.vue'
import RecycleBin from './components/RecycleBin.vue'
// 替换API引入为分文件导入
import {
  getImageList,
  createImage,
  updateImage,
  deleteImage,
  getImageDetail
} from '@/api/infra/materiallibrary/materialImage'
import {
  getVideoList,
  createVideo,
  updateVideo,
  deleteVideo,
  getVideoDetail
} from '@/api/infra/materiallibrary/materialVideo'
import {
  getVoiceList,
  createVoice,
  updateVoice,
  deleteVoice,
  getVoiceDetail
} from '@/api/infra/materiallibrary/materialVoice'
import {
  getArticleList,
  createArticle,
  updateArticle,
  deleteArticle,
  getArticleDetail
} from '@/api/infra/materiallibrary/materialArticle'
import {
  getDocumentList,
  createDocument,
  updateDocument,
  deleteDocument,
  getDocumentDetail
} from '@/api/infra/materiallibrary/materialDocument'
import {
  getNewsList,
  createNews,
  updateNews,
  deleteNews,
  getNewsDetail
} from '@/api/infra/materiallibrary/materialNews'
import {
  getCategoryList,
  createCategory,
  updateCategory,
  deleteCategory
} from '@/api/infra/materiallibrary/materialCategory'
import { getSimpleDeptList } from '@/api/system/dept'
import { handleTree } from '@/utils/tree'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

// 分类树相关
const categoryKeyword = ref('')
// categoryList 改为接口数据
const categoryList = ref<any[]>([])
const categoryTreeRef = ref<InstanceType<typeof ElTree>>()
const categoryTreeProps = { label: 'name', children: 'children' }
const filterCategoryNode = (keyword: string, data: any) => {
  if (!keyword) return true
  return data.name.includes(keyword)
}
const selectedCategoryId = ref<any>(undefined)

const filterVisibleOrgId = ref<any>(undefined)

const handleCategoryNodeClick = (node: any) => {
  if (typeof node.id === 'string' && node.id.startsWith('group-')) {
    // 机构分组节点
    const orgId = Number(node.id.replace('group-', ''))
    filterVisibleOrgId.value = orgId
    // 清空categoryId
    imageSearchForm.value.categoryId = undefined
    videoSearchForm.value.categoryId = undefined
    voiceSearchForm.value.categoryId = undefined
    articleSearchForm.value.categoryId = undefined
    documentSearchForm.value.categoryId = undefined
    newsSearchForm.value.categoryId = undefined
    // 不赋值visibleOrgId到表单
  } else {
    filterVisibleOrgId.value = undefined
    // 分类节点
    imageSearchForm.value.categoryId = node.id
    videoSearchForm.value.categoryId = node.id
    voiceSearchForm.value.categoryId = node.id
    articleSearchForm.value.categoryId = node.id
    documentSearchForm.value.categoryId = node.id
    newsSearchForm.value.categoryId = node.id
    // 清空visibleOrgId
    imageSearchForm.value.visibleOrgId = undefined
    videoSearchForm.value.visibleOrgId = undefined
    voiceSearchForm.value.visibleOrgId = undefined
    articleSearchForm.value.visibleOrgId = undefined
    documentSearchForm.value.visibleOrgId = undefined
    newsSearchForm.value.visibleOrgId = undefined
  }
  // 刷新当前tab
  if (activeTab.value === 'image') loadImageList()
  else if (activeTab.value === 'video') loadVideoList()
  else if (activeTab.value === 'voice') loadVoiceList()
  else if (activeTab.value === 'article') loadArticleList()
  else if (activeTab.value === 'doc') loadDocumentList()
  else if (activeTab.value === 'graphic') loadNewsList()
}

function groupCategoriesByVisibleOrg(list) {
  // 以visibleOrgId分组
  const groupMap = new Map()
  list.forEach((item) => {
    const groupId = item.visibleOrgId || 0
    const groupName = item.visibleOrgName || '未分组'
    if (!groupMap.has(groupId)) {
      groupMap.set(groupId, {
        id: 'group-' + groupId,
        name: groupName,
        children: []
      })
    }
    groupMap.get(groupId).children.push(item)
  })
  return Array.from(groupMap.values())
}

async function loadCategoryList() {
  try {
    const res = await getCategoryList()
    console.log('res', res)
    let rawList: any[] = []
    if (Array.isArray(res)) {
      rawList = res
    } else if (res && Array.isArray(res.data)) {
      rawList = res.data
    }
    categoryList.value = groupCategoriesByVisibleOrg(rawList)
    console.log('categoryList', categoryList.value)
  } catch (e) {
    // 这里可以加全局错误提示
    console.error('获取分类列表失败', e)
    categoryList.value = []
  }
}
// 页面加载时拉取分类列表
loadCategoryList()

// 新增分类弹窗相关
const dialogVisible = ref(false)
const dialogTitle = ref('新增图文分类')
const isEditMode = ref(false)
const categoryForm = ref<any>({
  id: undefined,
  name: '',
  parentId: 0,
  sort: 0,
  status: 1,
  description: '',
  visibleOrgId: undefined,
  visibleOrgName: ''
})
const categoryFormRef = ref()
const categoryRules = {
  name: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
  visibleOrgId: [{ required: true, message: '请选择可见范围', trigger: 'change' }]
}

// 部门树相关数据
const deptList = ref<any[]>([])
const deptTreeRef = ref<InstanceType<typeof ElTree>>()
const deptSearchKeyword = ref('')
const selectedDept = ref<any>(null)

// 获取部门树数据
async function loadDeptList() {
  try {
    const res = await getSimpleDeptList()
    // 过滤出顶级部门(parentId=0)和其直接子部门
    const topLevelDepts = res.filter((dept: any) => dept.parentId === 0)
    const filteredDepts = topLevelDepts.map((topDept: any) => {
      const children = res.filter((dept: any) => dept.parentId === topDept.id)
      return {
        ...topDept,
        children: children.length > 0 ? children : undefined
      }
    })
    deptList.value = filteredDepts
  } catch (e) {
    console.error('获取部门列表失败', e)
    deptList.value = []
  }
}

// 部门树过滤方法
const filterDeptNode = (keyword: string, data: any) => {
  if (!keyword) return true
  return data.name.includes(keyword)
}

// 部门树点击事件
const handleDeptNodeClick = (row: any) => {
  if (row.parentId === 0) {
    ElMessage.warning('请选择具体机构')
    return
  }
  selectedDept.value = row
  categoryForm.value.visibleOrgId = row.id
  categoryForm.value.visibleOrgName = row.name
}

// 监听部门搜索关键字
watch(deptSearchKeyword, (val) => {
  deptTreeRef.value?.filter(val)
})

// 监听分类搜索关键字
watch(categoryKeyword, (val) => {
  categoryTreeRef.value?.filter(val)
})

// 新增分类
async function handleCategorySave() {
  categoryFormRef.value.validate(async (valid: boolean) => {
    if (!valid) return
    try {
      if (isEditMode.value) {
        await updateCategory({
          id: categoryForm.value.id,
          name: categoryForm.value.name,
          parentId: categoryForm.value.parentId,
          sort: categoryForm.value.sort,
          status: categoryForm.value.status,
          description: categoryForm.value.description,
          visibleOrgId: categoryForm.value.visibleOrgId,
          visibleOrgName: categoryForm.value.visibleOrgName
        })
      } else {
        await createCategory({
          name: categoryForm.value.name,
          parentId: categoryForm.value.parentId,
          sort: categoryForm.value.sort,
          status: categoryForm.value.status,
          description: categoryForm.value.description,
          visibleOrgId: categoryForm.value.visibleOrgId,
          visibleOrgName: categoryForm.value.visibleOrgName
        })
      }
      dialogVisible.value = false
      categoryForm.value.name = ''
      categoryForm.value.visibleOrgId = undefined
      categoryForm.value.visibleOrgName = ''
      selectedDept.value = null
      // 新增成功后刷新分类树
      loadCategoryList()
    } catch (e) {
      console.error('新增分类失败', e)
    }
  })
}

// 打开新增分类弹窗时加载部门数据
const openCategoryDialog = () => {
  dialogVisible.value = true
  dialogTitle.value = '新增图文分类'
  isEditMode.value = false
  // 重置表单
  categoryForm.value = {
    id: undefined,
    name: '',
    parentId: 0,
    sort: 0,
    status: 1,
    description: '',
    visibleOrgId: undefined,
    visibleOrgName: ''
  }
  selectedDept.value = null
  loadDeptList()
}

// 编辑分类（假设有编辑按钮时调用此方法，弹窗回填数据）
function handleEditCategory(row) {
  dialogVisible.value = true
  dialogTitle.value = '编辑分类'
  isEditMode.value = true
  // 复制数据，确保包含id字段
  categoryForm.value = {
    id: row.id,
    name: row.name,
    parentId: row.parentId || 0,
    sort: row.sort || 0,
    status: row.status || 1,
    description: row.description || '',
    visibleOrgId: row.visibleOrgId,
    visibleOrgName: row.visibleOrgName || ''
  }
  // 先加载部门数据，然后回填
  loadDeptList().then(() => {
    if (row.visibleOrgId) {
      // 递归查找选中的部门
      const findDept = (depts: any[], targetId: number): any => {
        for (const dept of depts) {
          if (dept.id === targetId) {
            return dept
          }
          if (dept.children) {
            const found = findDept(dept.children, targetId)
            if (found) return found
          }
        }
        return null
      }

      const foundDept = findDept(deptList.value, row.visibleOrgId)
      if (foundDept) {
        selectedDept.value = foundDept
        categoryForm.value.visibleOrgId = foundDept.id
        categoryForm.value.visibleOrgName = foundDept.name
      }
    }
  })
}
// 保存编辑（与新增共用 handleCategorySave，根据有无 id 判断）
// 删除分类
async function handleDeleteCategory(row) {
  try {
    await ElMessageBox.confirm('确定要删除这个分类吗？', '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await deleteCategory(row.id)
    ElMessage.success('删除成功')
    // 删除成功后刷新分类树
    loadCategoryList()
  } catch (e) {
    if (e !== 'cancel') {
      ElMessage.error('删除失败')
      console.error('删除分类失败', e)
    }
  }
}

// tab 切换
const activeTab = ref('image')

// 图片tab相关数据
const imageList = ref<any[]>([])
const imageTotal = ref(0)
const imagePage = ref(1)
const imagePageSize = ref(10)
const imageLoading = ref(false)
const imageSearchForm = ref({
  name: '',
  visibleOrgId: undefined,
  categoryId: undefined,
  visibleOrgName: undefined
})

// 来源下拉相关
const imageSourceOptions = ref<any[]>([])
const imageSourceLoading = ref(false)
const imageSourceLoaded = ref(false)

// 点击下拉时加载机构
async function handleImageSourceDropdown(visible: boolean) {
  if (visible && !imageSourceLoaded.value) {
    imageSourceLoading.value = true
    const res = await getSimpleDeptList()
    // 找出所有顶级机构id及名称
    const topLevels = res.filter((dept: any) => dept.parentId === 0)
    const topLevelMap = Object.fromEntries(topLevels.map((dept: any) => [dept.id, dept.name]))
    // 只展示直属子机构（parentId等于顶级机构id）
    const children = res.filter((dept: any) =>
      Object.keys(topLevelMap).map(Number).includes(dept.parentId)
    )
    imageSourceOptions.value = children.map((child) => ({
      label: `${topLevelMap[child.parentId]}/${child.name}`,
      value: child.id
    }))
    imageSourceLoading.value = false
    imageSourceLoaded.value = true
  }
}

// 获取图片列表
async function loadImageList() {
  imageLoading.value = true
  let visibleOrgId = imageSearchForm.value.visibleOrgId
  if (visibleOrgId === '') visibleOrgId = undefined
  const params = {
    pageNo: imagePage.value,
    pageSize: imagePageSize.value,
    name: imageSearchForm.value.name,
    visibleOrgId: filterVisibleOrgId.value ?? visibleOrgId,
    categoryId: imageSearchForm.value.categoryId,
    visibleOrgName: imageSearchForm.value.visibleOrgName
  }
  const res = await getImageList(params)
  console.log('res', res)
  // 兼容多种返回格式
  if (Array.isArray(res)) {
    imageList.value = res
    imageTotal.value = res.length
  } else if (res && Array.isArray(res.data)) {
    imageList.value = res.data
    imageTotal.value = res.data.length
  } else if (res && res.data && Array.isArray(res.data.list)) {
    imageList.value = res.data.list
    imageTotal.value = res.data.total || res.data.list.length
  } else if (res && Array.isArray(res.list)) {
    imageList.value = res.list
    imageTotal.value = res.total || res.list.length
  } else {
    imageList.value = []
    imageTotal.value = 0
  }
  imageLoading.value = false
}

// 搜索、重置、分页
function handleImageSearch() {
  imagePage.value = 1
  loadImageList()
}
function handleImageReset() {
  imageSearchForm.value.name = ''
  imageSearchForm.value.visibleOrgId = undefined
  imageSearchForm.value.categoryId = undefined
  imageSearchForm.value.visibleOrgName = undefined
  imagePage.value = 1
  loadImageList()
}
function handleImagePageChange({ page: newPage, limit: newLimit }) {
  imagePage.value = newPage
  imagePageSize.value = newLimit
  loadImageList()
}

// 页面加载时拉取图片列表
watchEffect(() => {
  if (activeTab.value === 'image') {
    loadImageList()
  }
})

// 视频tab相关数据
const videoList = ref<any[]>([])
const videoTotal = ref(0)
const videoPage = ref(1)
const videoPageSize = ref(10)
const videoLoading = ref(false)
const videoSearchForm = ref({
  name: '',
  visibleOrgId: undefined,
  categoryId: undefined,
  visibleOrgName: undefined
})
const videoSourceOptions = ref<any[]>([])
const videoSourceLoading = ref(false)
const videoSourceLoaded = ref(false)
async function handleVideoSourceDropdown(visible: boolean) {
  if (visible && !videoSourceLoaded.value) {
    videoSourceLoading.value = true
    const res = await getSimpleDeptList()
    const topLevels = res.filter((dept: any) => dept.parentId === 0)
    const topLevelMap = Object.fromEntries(topLevels.map((dept: any) => [dept.id, dept.name]))
    const children = res.filter((dept: any) =>
      Object.keys(topLevelMap).map(Number).includes(dept.parentId)
    )
    videoSourceOptions.value = children.map((child) => ({
      label: `${topLevelMap[child.parentId]}/${child.name}`,
      value: child.id
    }))
    videoSourceLoading.value = false
    videoSourceLoaded.value = true
  }
}

// 获取视频列表
async function loadVideoList() {
  videoLoading.value = true
  let visibleOrgId = videoSearchForm.value.visibleOrgId
  if (visibleOrgId === '') visibleOrgId = undefined
  const params = {
    pageNo: videoPage.value,
    pageSize: videoPageSize.value,
    name: videoSearchForm.value.name,
    visibleOrgId: filterVisibleOrgId.value ?? visibleOrgId,
    categoryId: videoSearchForm.value.categoryId,
    visibleOrgName: videoSearchForm.value.visibleOrgName
  }
  const res = await getVideoList(params)
  // 兼容多种返回格式
  if (Array.isArray(res)) {
    videoList.value = res
    videoTotal.value = res.length
  } else if (res && Array.isArray(res.data)) {
    videoList.value = res.data
    videoTotal.value = res.data.length
  } else if (res && res.data && Array.isArray(res.data.list)) {
    videoList.value = res.data.list
    videoTotal.value = res.data.total || res.data.list.length
  } else if (res && Array.isArray(res.list)) {
    videoList.value = res.list
    videoTotal.value = res.total || res.list.length
  } else {
    videoList.value = []
    videoTotal.value = 0
  }
  videoLoading.value = false
}

// 搜索、重置、分页
function handleVideoSearch() {
  videoPage.value = 1
  loadVideoList()
}
function handleVideoReset() {
  videoSearchForm.value.name = ''
  videoSearchForm.value.visibleOrgId = undefined
  videoSearchForm.value.categoryId = undefined
  videoSearchForm.value.visibleOrgName = undefined
  videoPage.value = 1
  loadVideoList()
}
function handleVideoPageChange({ page: newPage, limit: newLimit }) {
  videoPage.value = newPage
  videoPageSize.value = newLimit
  loadVideoList()
}

// 页面加载时拉取视频列表
watchEffect(() => {
  if (activeTab.value === 'video') {
    loadVideoList()
  }
})

// 音频tab相关数据
const voiceList = ref<any[]>([])
const voiceTotal = ref(0)
const voicePage = ref(1)
const voicePageSize = ref(10)
const voiceLoading = ref(false)
const voiceSearchForm = ref({
  name: '',
  visibleOrgId: undefined,
  categoryId: undefined,
  visibleOrgName: undefined
})
const voiceSourceOptions = ref<any[]>([])
const voiceSourceLoading = ref(false)
const voiceSourceLoaded = ref(false)

async function handleVoiceSourceDropdown(visible: boolean) {
  if (visible && !voiceSourceLoaded.value) {
    voiceSourceLoading.value = true
    const res = await getSimpleDeptList()
    const topLevels = res.filter((dept: any) => dept.parentId === 0)
    const topLevelMap = Object.fromEntries(topLevels.map((dept: any) => [dept.id, dept.name]))
    const children = res.filter((dept: any) =>
      Object.keys(topLevelMap).map(Number).includes(dept.parentId)
    )
    voiceSourceOptions.value = children.map((child) => ({
      label: `${topLevelMap[child.parentId]}/${child.name}`,
      value: child.id
    }))
    voiceSourceLoading.value = false
    voiceSourceLoaded.value = true
  }
}

// 获取音频列表
async function loadVoiceList() {
  voiceLoading.value = true
  let visibleOrgId = voiceSearchForm.value.visibleOrgId
  if (visibleOrgId === '') visibleOrgId = undefined
  const params = {
    pageNo: voicePage.value,
    pageSize: voicePageSize.value,
    name: voiceSearchForm.value.name,
    visibleOrgId: filterVisibleOrgId.value ?? visibleOrgId,
    categoryId: voiceSearchForm.value.categoryId,
    visibleOrgName: voiceSearchForm.value.visibleOrgName
  }
  const res = await getVoiceList(params)
  // 兼容多种返回格式
  if (Array.isArray(res)) {
    voiceList.value = res
    voiceTotal.value = res.length
  } else if (res && Array.isArray(res.data)) {
    voiceList.value = res.data
    voiceTotal.value = res.data.length
  } else if (res && res.data && Array.isArray(res.data.list)) {
    voiceList.value = res.data.list
    voiceTotal.value = res.data.total || res.data.list.length
  } else if (res && Array.isArray(res.list)) {
    voiceList.value = res.list
    voiceTotal.value = res.total || res.list.length
  } else {
    voiceList.value = []
    voiceTotal.value = 0
  }
  voiceLoading.value = false
}

// 搜索、重置、分页
function handleVoiceSearch() {
  voicePage.value = 1
  loadVoiceList()
}
function handleVoiceReset() {
  voiceSearchForm.value.name = ''
  voiceSearchForm.value.visibleOrgId = undefined
  voiceSearchForm.value.categoryId = undefined
  voiceSearchForm.value.visibleOrgName = undefined
  voicePage.value = 1
  loadVoiceList()
}
function handleVoicePageChange({ page: newPage, limit: newLimit }) {
  voicePage.value = newPage
  voicePageSize.value = newLimit
  loadVoiceList()
}

// 页面加载时拉取音频列表
watchEffect(() => {
  if (activeTab.value === 'voice') {
    loadVoiceList()
  }
})

// 文章tab相关数据
const articleList = ref<any[]>([])
const articleTotal = ref(0)
const articlePage = ref(1)
const articlePageSize = ref(10)
const articleLoading = ref(false)
const articleSearchForm = ref({
  title: '',
  visibleOrgId: undefined,
  categoryId: undefined,
  visibleOrgName: undefined
})
const articleSourceOptions = ref<any[]>([])
const articleSourceLoading = ref(false)
const articleSourceLoaded = ref(false)
async function handleArticleSourceDropdown(visible: boolean) {
  if (visible && !articleSourceLoaded.value) {
    articleSourceLoading.value = true
    const res = await getSimpleDeptList()
    const topLevels = res.filter((dept: any) => dept.parentId === 0)
    const topLevelMap = Object.fromEntries(topLevels.map((dept: any) => [dept.id, dept.name]))
    const children = res.filter((dept: any) =>
      Object.keys(topLevelMap).map(Number).includes(dept.parentId)
    )
    articleSourceOptions.value = children.map((child) => ({
      label: `${topLevelMap[child.parentId]}/${child.name}`,
      value: child.id
    }))
    articleSourceLoading.value = false
    articleSourceLoaded.value = true
  }
}

// 获取文章列表
async function loadArticleList() {
  articleLoading.value = true
  let visibleOrgId = articleSearchForm.value.visibleOrgId
  if (visibleOrgId === '') visibleOrgId = undefined
  const params = {
    pageNo: articlePage.value,
    pageSize: articlePageSize.value,
    title: articleSearchForm.value.title,
    visibleOrgId: filterVisibleOrgId.value ?? visibleOrgId,
    categoryId: articleSearchForm.value.categoryId,
    visibleOrgName: articleSearchForm.value.visibleOrgName
  }
  const res = await getArticleList(params)
  // 兼容多种返回格式
  if (Array.isArray(res)) {
    articleList.value = res
    articleTotal.value = res.length
  } else if (res && Array.isArray(res.data)) {
    articleList.value = res.data
    articleTotal.value = res.data.length
  } else if (res && res.data && Array.isArray(res.data.list)) {
    articleList.value = res.data.list
    articleTotal.value = res.data.total || res.data.list.length
  } else if (res && Array.isArray(res.list)) {
    articleList.value = res.list
    articleTotal.value = res.total || res.list.length
  } else {
    articleList.value = []
    articleTotal.value = 0
  }
  articleLoading.value = false
}

// 搜索、重置、分页
function handleArticleSearch() {
  articlePage.value = 1
  loadArticleList()
}
function handleArticleReset() {
  articleSearchForm.value.title = ''
  articleSearchForm.value.visibleOrgId = undefined
  articleSearchForm.value.categoryId = undefined
  articleSearchForm.value.visibleOrgName = undefined
  articlePage.value = 1
  loadArticleList()
}
function handleArticlePageChange({ page: newPage, limit: newLimit }) {
  articlePage.value = newPage
  articlePageSize.value = newLimit
  loadArticleList()
}

// 页面加载时拉取文章列表
watchEffect(() => {
  if (activeTab.value === 'article') {
    loadArticleList()
  }
})

// 文档tab相关数据
const documentList = ref<any[]>([])
const documentTotal = ref(0)
const documentPage = ref(1)
const documentPageSize = ref(10)
const documentLoading = ref(false)
const documentSearchForm = ref({
  name: '',
  visibleOrgId: undefined,
  categoryId: undefined,
  visibleOrgName: undefined
})
const documentSourceOptions = ref<any[]>([])
const documentSourceLoading = ref(false)
const documentSourceLoaded = ref(false)
async function handleDocumentSourceDropdown(visible: boolean) {
  if (visible && !documentSourceLoaded.value) {
    documentSourceLoading.value = true
    const res = await getSimpleDeptList()
    const topLevels = res.filter((dept: any) => dept.parentId === 0)
    const topLevelMap = Object.fromEntries(topLevels.map((dept: any) => [dept.id, dept.name]))
    const children = res.filter((dept: any) =>
      Object.keys(topLevelMap).map(Number).includes(dept.parentId)
    )
    documentSourceOptions.value = children.map((child) => ({
      label: `${topLevelMap[child.parentId]}/${child.name}`,
      value: child.id
    }))
    documentSourceLoading.value = false
    documentSourceLoaded.value = true
  }
}

// 获取文档列表
async function loadDocumentList() {
  documentLoading.value = true
  let visibleOrgId = documentSearchForm.value.visibleOrgId
  if (visibleOrgId === '') visibleOrgId = undefined
  const params = {
    pageNo: documentPage.value,
    pageSize: documentPageSize.value,
    name: documentSearchForm.value.name,
    visibleOrgId: filterVisibleOrgId.value ?? visibleOrgId,
    categoryId: documentSearchForm.value.categoryId,
    visibleOrgName: documentSearchForm.value.visibleOrgName
  }
  const res = await getDocumentList(params)
  // 兼容多种返回格式
  if (Array.isArray(res)) {
    documentList.value = res
    documentTotal.value = res.length
  } else if (res && Array.isArray(res.data)) {
    documentList.value = res.data
    documentTotal.value = res.data.length
  } else if (res && res.data && Array.isArray(res.data.list)) {
    documentList.value = res.data.list
    documentTotal.value = res.data.total || res.data.list.length
  } else if (res && Array.isArray(res.list)) {
    documentList.value = res.list
    documentTotal.value = res.total || res.list.length
  } else {
    documentList.value = []
    documentTotal.value = 0
  }
  documentLoading.value = false
}

// 搜索、重置、分页
function handleDocumentSearch() {
  documentPage.value = 1
  loadDocumentList()
}
function handleDocumentReset() {
  documentSearchForm.value.name = ''
  documentSearchForm.value.visibleOrgId = undefined
  documentSearchForm.value.categoryId = undefined
  documentSearchForm.value.visibleOrgName = undefined
  documentPage.value = 1
  loadDocumentList()
}
function handleDocumentPageChange({ page: newPage, limit: newLimit }) {
  documentPage.value = newPage
  documentPageSize.value = newLimit
  loadDocumentList()
}

// 页面加载时拉取文档列表
watchEffect(() => {
  if (activeTab.value === 'doc') {
    loadDocumentList()
  }
})

// 图文tab相关数据
const newsList = ref<any[]>([])
const newsTotal = ref(0)
const newsPage = ref(1)
const newsPageSize = ref(10)
const newsLoading = ref(false)
const newsSearchForm = ref({
  title: '',
  visibleOrgId: undefined,
  categoryId: undefined,
  visibleOrgName: undefined
})
const newsSourceOptions = ref<any[]>([])
const newsSourceLoading = ref(false)
const newsSourceLoaded = ref(false)
async function handleNewsSourceDropdown(visible: boolean) {
  if (visible && !newsSourceLoaded.value) {
    newsSourceLoading.value = true
    const res = await getSimpleDeptList()
    const topLevels = res.filter((dept: any) => dept.parentId === 0)
    const topLevelMap = Object.fromEntries(topLevels.map((dept: any) => [dept.id, dept.name]))
    const children = res.filter((dept: any) =>
      Object.keys(topLevelMap).map(Number).includes(dept.parentId)
    )
    newsSourceOptions.value = children.map((child) => ({
      label: `${topLevelMap[child.parentId]}/${child.name}`,
      value: child.id
    }))
    newsSourceLoading.value = false
    newsSourceLoaded.value = true
  }
}

// 获取图文列表
async function loadNewsList() {
  newsLoading.value = true
  let visibleOrgId = newsSearchForm.value.visibleOrgId
  if (visibleOrgId === '') visibleOrgId = undefined
  const params = {
    pageNo: newsPage.value,
    pageSize: newsPageSize.value,
    title: newsSearchForm.value.title,
    visibleOrgId: filterVisibleOrgId.value ?? visibleOrgId,
    categoryId: newsSearchForm.value.categoryId,
    visibleOrgName: newsSearchForm.value.visibleOrgName
  }
  const res = await getNewsList(params)
  // 兼容多种返回格式
  if (Array.isArray(res)) {
    newsList.value = res
    newsTotal.value = res.length
  } else if (res && Array.isArray(res.data)) {
    newsList.value = res.data
    newsTotal.value = res.data.length
  } else if (res && res.data && Array.isArray(res.data.list)) {
    newsList.value = res.data.list
    newsTotal.value = res.data.total || res.data.list.length
  } else if (res && Array.isArray(res.list)) {
    newsList.value = res.list
    newsTotal.value = res.total || res.list.length
  } else {
    newsList.value = []
    newsTotal.value = 0
  }
  newsLoading.value = false
}

// 搜索、重置、分页
function handleNewsSearch() {
  newsPage.value = 1
  loadNewsList()
}
function handleNewsReset() {
  newsSearchForm.value.title = ''
  newsSearchForm.value.visibleOrgId = undefined
  newsSearchForm.value.categoryId = undefined
  newsSearchForm.value.visibleOrgName = undefined
  newsPage.value = 1
  loadNewsList()
}
function handleNewsPageChange({ page: newPage, limit: newLimit }) {
  newsPage.value = newPage
  newsPageSize.value = newLimit
  loadNewsList()
}

// 页面加载时拉取图文列表
watchEffect(() => {
  if (activeTab.value === 'graphic') {
    loadNewsList()
  }
})

const addPictureRef = ref()
const addVideoRef = ref()
const addVoiceRef = ref()
const addArticleRef = ref()
const addDocumentRef = ref()
const addImageTextRef = ref()

function handleAddMaterial() {
  addPictureRef.value && addPictureRef.value.open()
}

function handleAddVideo() {
  addVideoRef.value && addVideoRef.value.open()
}

function handleAddVoice() {
  addVoiceRef.value && addVoiceRef.value.open()
}

function handleAddArticle() {
  addArticleRef.value && addArticleRef.value.open()
}

function handleAddDoc() {
  addDocumentRef.value && addDocumentRef.value.open()
}

function handleAddGraphic() {
  addImageTextRef.value && addImageTextRef.value.open()
}

async function handleEditArticle(row) {
  if (!row.id) return
  const res = await getArticleDetail(row.id)
  let data = res
  if (res && res.data) data = res.data
  addArticleRef.value &&
    addArticleRef.value.open({
      id: data.id,
      name: data.title || data.name || '',
      coverUrl: data.coverUrl || data.imgUrl || data.img || '',
      content: data.content || '',
      scope: data.scope || '',
      group: data.group || ''
    })
}

async function handleEditDoc(row) {
  if (!row.id) return
  const res = await getDocumentDetail(row.id)
  let data = res
  if (res && res.data) data = res.data
  addDocumentRef.value &&
    addDocumentRef.value.open({
      id: data.id,
      name: data.documentName || data.name || '',
      docFileName: data.documentUrl || data.docFileName || '',
      scope: data.scope || '',
      group: data.group || ''
    })
}

async function handleEditGraphic(row) {
  if (!row.id) return
  const res = await getNewsDetail(row.id)
  let data = res
  if (res && res.data) data = res.data
  addImageTextRef.value &&
    addImageTextRef.value.open({
      id: data.id,
      name: data.name || '',
      thumbUrl: data.thumbUrl || data.imgUrl || data.url || '',
      graphicText: data.content || data.graphicText || data.description || '',
      group: data.group || '',
      scope: data.scope || ''
    })
}

function handleEditPicture(row) {
  addPictureRef.value &&
    addPictureRef.value.open({
      id: row.id,
      name: row.name,
      url: row.url,
      // 其他字段可按需补充
      ...row
    })
}

function handleEditVideo(row) {
  addVideoRef.value &&
    addVideoRef.value.open({
      id: row.id,
      name: row.name,
      videoName: row.videoName,
      videoUrl: row.videoUrl || row.url,
      thumbUrl: row.thumbUrl || row.thumb || row.imgUrl || '',
      scope: row.scope || '',
      group: row.group || ''
    })
}

function handleEditVoice(row) {
  addVoiceRef.value &&
    addVoiceRef.value.open({
      id: row.id,
      name: row.name,
      audioName: row.audioName,
      audioUrl: row.audioUrl || row.url,
      scope: row.scope || '',
      group: row.group || '',
      duration: row.duration,
      bitrate: row.bitrate,
      format: row.format
    })
}

// 格式化音频时长显示
function formatDuration(seconds: number): string {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 删除图片
async function handleDeletePicture(row) {
  try {
    if (!row.id) {
      ElMessage.error('图片ID缺失，无法删除')
      return
    }
    await deleteImage(row.id)
    ElMessage.success('删除成功')
    loadImageList()
  } catch (e) {
    ElMessage.error('删除失败')
    console.error('删除图片失败', e)
  }
}

// 删除视频
async function handleDeleteVideo(row) {
  try {
    await deleteVideo(row.id)
    ElMessage.success('删除成功')
    loadVideoList()
  } catch (e) {
    ElMessage.error('删除失败')
    console.error('删除视频失败', e)
  }
}

// 删除音频
async function handleDeleteVoice(row) {
  try {
    await deleteVoice(row.id)
    ElMessage.success('删除成功')
    loadVoiceList()
  } catch (e) {
    ElMessage.error('删除失败')
    console.error('删除音频失败', e)
  }
}

// 删除文章
async function handleDeleteArticle(row) {
  try {
    await deleteArticle(row.id)
    ElMessage.success('删除成功')
    loadArticleList()
  } catch (e) {
    ElMessage.error('删除失败')
    console.error('删除文章失败', e)
  }
}

// 删除文档
async function handleDeleteDocument(row) {
  try {
    await deleteDocument(row.id)
    ElMessage.success('删除成功')
    loadDocumentList()
  } catch (e) {
    ElMessage.error('删除失败')
    console.error('删除文档失败', e)
  }
}

// 删除图文
async function handleDeleteNews(row) {
  try {
    await deleteNews(row.id)
    ElMessage.success('删除成功')
    loadNewsList()
  } catch (e) {
    ElMessage.error('删除失败')
    console.error('删除图文失败', e)
  }
}

function handleCategoryReset() {
  selectedCategoryId.value = undefined
  filterVisibleOrgId.value = undefined
  imageSearchForm.value.categoryId = undefined
  videoSearchForm.value.categoryId = undefined
  voiceSearchForm.value.categoryId = undefined
  articleSearchForm.value.categoryId = undefined
  documentSearchForm.value.categoryId = undefined
  newsSearchForm.value.categoryId = undefined
  // 刷新当前tab
  if (activeTab.value === 'image') loadImageList()
  else if (activeTab.value === 'video') loadVideoList()
  else if (activeTab.value === 'voice') loadVoiceList()
  else if (activeTab.value === 'article') loadArticleList()
  else if (activeTab.value === 'doc') loadDocumentList()
  else if (activeTab.value === 'graphic') loadNewsList()
}

const handleDeptTagClose = () => {
  selectedDept.value = null
  categoryForm.value.visibleOrgId = undefined
  categoryForm.value.visibleOrgName = ''
}
</script>

<style scoped>
.material-library-container {
  background: #fff;
  padding: 24px;
  border-radius: 8px;
}
.search-bar {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.dept-tree-container {
  width: 100%;
}

.dept-tree-wrapper {
  margin-bottom: 10px;
}

.selected-dept-info {
  margin-top: 8px;
}

.selected-dept {
  color: #409eff;
  font-weight: 500;
}

.category-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-right: 8px;
}

.category-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.category-actions {
  display: flex;
  align-items: center;
  opacity: 0;
  transition: opacity 0.2s;
}

.category-node:hover .category-actions {
  opacity: 1;
}
</style>
