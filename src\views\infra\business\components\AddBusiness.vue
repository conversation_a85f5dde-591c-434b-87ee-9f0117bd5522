<!--
  组件名称：AddBusiness
  功能描述：新建商机抽屉表单，包含商机名称、关联客户、业务模块、预计成交金额、预计成交日期、销售阶段、销售负责人、商机描述等字段，支持表单校验、重置、取消、保存
-->
<template>
  <el-drawer
    v-model="visible"
    :title="drawerTitle"
    direction="rtl"
    size="500px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="110px"
      label-position="top"
      style="margin-right: 16px"
    >
      <el-form-item label="商机名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="例如：XX大学2024年春季实践项目"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="关联客户" prop="customerName">
        <el-input v-model="form.customerName" placeholder="必填，请从资源库选择" />
      </el-form-item>
      <el-form-item label="业务模块" prop="businessType">
        <el-select v-model="form.businessType" placeholder="请选择业务模块">
          <el-option
            v-for="item in businessTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="预计成交金额(元)" prop="totalPrice">
        <el-input
          v-model.number="form.totalPrice"
          placeholder="必填, 必须为正数"
          type="number"
          min="0"
        />
      </el-form-item>
      <el-form-item label="预计成交日期" prop="expectedDealDate">
        <el-date-picker
          v-model="form.expectedDealDate"
          type="date"
          placeholder="年/月/日"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="销售阶段" prop="businessStage">
        <el-select v-model="form.businessStage" placeholder="请选择销售阶段">
          <el-option
            v-for="item in businessStageOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="销售负责人" prop="ownerUserId">
        <el-select v-model="form.ownerUserId" placeholder="请选择负责人">
          <el-option
            v-for="user in userOptions"
            :key="user.id"
            :label="user.nickname || user.username"
            :value="user.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="商机描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          maxlength="200"
          show-word-limit
          placeholder="请输入商机描述"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div style="text-align: right">
        <el-button @click="onCancel">取消</el-button>
        <el-button v-if="!editingId" @click="onReset">重置</el-button>
        <el-button type="primary" @click="onSubmit">保存</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineExpose, getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'
import { getDictDataPage } from '@/api/system/dict/dict.data'
import { getSimpleUserList } from '@/api/system/user'
import request from '@/config/axios'

let editingId: string | null = null

// 控制抽屉显示
const visible = ref(false)

// 抽屉标题
const drawerTitle = ref('新建商机')

// 下拉选项
const businessTypeOptions = ref<any[]>([])
const businessStageOptions = ref<any[]>([])
const userOptions = ref<any[]>([])

// 表单数据
interface BusinessForm {
  name: string
  customerId: string
  customerName: string
  businessType: string
  totalPrice: number | undefined
  expectedDealDate: string
  businessStage: string
  ownerUserId: string
  ownerUserName: string
  description: string
}
const defaultForm: BusinessForm = {
  name: '',
  customerId: '',
  customerName: '',
  businessType: '',
  totalPrice: undefined,
  expectedDealDate: '',
  businessStage: '',
  ownerUserId: '',
  ownerUserName: '',
  description: ''
}
const form = reactive<BusinessForm>({ ...defaultForm })

// 表单校验规则
const rules = {
  name: [{ required: true, message: '请输入商机名称', trigger: 'blur' }],
  customerName: [{ required: true, message: '请选择关联客户', trigger: 'blur' }],
  businessType: [{ required: true, message: '请选择业务模块', trigger: 'change' }],
  totalPrice: [
    { required: true, type: 'number' as const, message: '请输入预计成交金额', trigger: 'blur' },
    { type: 'number' as const, min: 0, message: '金额必须为正数', trigger: 'blur' }
  ],
  expectedDealDate: [{ required: true, message: '请选择预计成交日期', trigger: 'change' }],
  businessStage: [{ required: true, message: '请选择销售阶段', trigger: 'change' }],
  ownerUserId: [{ required: true, message: '请选择销售负责人', trigger: 'change' }]
}

const formRef = ref()

// 获取下拉选项
const fetchOptions = async () => {
  // 业务模块
  const businessModuleRes = await getDictDataPage({
    dictType: 'business_module',
    pageNo: 1,
    pageSize: 100
  } as any)
  businessTypeOptions.value = (businessModuleRes?.list || []).map((item) => ({
    label: item.label,
    value: item.value
  }))
  // 销售阶段
  const salesStageRes = await getDictDataPage({
    dictType: 'business_sales_stage',
    pageNo: 1,
    pageSize: 100
  } as any)
  businessStageOptions.value = (salesStageRes?.list || []).map((item) => ({
    label: item.label,
    value: item.value
  }))
  // 负责人
  const userRes = await getSimpleUserList()
  userOptions.value = userRes
}

onMounted(() => {
  fetchOptions()
})

// 对外暴露方法，供父组件控制显示
async function open(business: any = null) {
  visible.value = true
  if (business && business.id) {
    // 编辑模式，通过ID查详情
    editingId = business.id
    drawerTitle.value = '更新商机'
    const detailRes = await request.get({
      url: '/publicbiz/business/detail',
      params: { id: business.id }
    })
    const detail = detailRes?.business || detailRes?.data?.business || {}
    Object.assign(form, {
      name: detail.name || '',
      customerId: detail.customerId || '',
      customerName: detail.customerName || '',
      businessType: detail.businessType || '',
      totalPrice: detail.totalPrice,
      expectedDealDate: detail.expectedDealDate || '',
      businessStage: detail.businessStage || '',
      ownerUserId: detail.ownerUserId || '',
      ownerUserName: detail.ownerUserName || '',
      description: detail.description || ''
    })
  } else {
    // 新建模式，重置表单
    editingId = null
    drawerTitle.value = '新建商机'
    Object.assign(form, defaultForm)
    onReset()
  }
}
const emit = getCurrentInstance()?.emit || (() => {})
function close() {
  visible.value = false
  emit('close')
}

// 取消
function onCancel() {
  close()
}
// 重置
function onReset() {
  if (formRef.value) formRef.value.resetFields()
}
// 保存
async function onSubmit() {
  formRef.value.validate(async (valid: boolean) => {
    if (!valid) return
    // 构造接口参数
    const data = {
      id: editingId || undefined,
      name: form.name,
      customerId: form.customerId,
      customerName: form.customerName,
      businessType: form.businessType,
      totalPrice: form.totalPrice,
      expectedDealDate: form.expectedDealDate,
      businessStage: form.businessStage,
      ownerUserId: form.ownerUserId,
      ownerUserName: userOptions.value.find((u) => u.id == form.ownerUserId)?.nickname || '',
      description: form.description
    }
    if (editingId) {
      await request.post({ url: '/publicbiz/business/update', data })
      ElMessage.success('更新成功')
    } else {
      await request.post({ url: '/publicbiz/business/create', data })
      ElMessage.success('保存成功')
    }
    close()
  })
}

defineExpose({ open, close })
</script>

<style scoped lang="scss">
.el-drawer__body {
  padding-bottom: 0;
}
</style>
