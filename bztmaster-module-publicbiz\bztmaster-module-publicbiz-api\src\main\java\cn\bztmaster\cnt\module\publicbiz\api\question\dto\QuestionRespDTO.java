package cn.bztmaster.cnt.module.publicbiz.api.question.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 考题管理 Response DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "考题管理 Response DTO")
public class QuestionRespDTO {

    @Schema(description = "主键ID", example = "1")
    private Long id;

    @Schema(description = "租户ID", example = "1")
    private Long tenantId;

    @Schema(description = "一级分类名称", example = "职业技能等级认定")
    private String level1Name;

    @Schema(description = "一级分类代码", example = "ZY001")
    private String level1Code;

    @Schema(description = "二级分类名称", example = "家政服务类")
    private String level2Name;

    @Schema(description = "二级分类代码", example = "JZ001")
    private String level2Code;

    @Schema(description = "三级分类名称", example = "家政服务员")
    private String level3Name;

    @Schema(description = "三级分类代码", example = "JZFW001")
    private String level3Code;

    @Schema(description = "认定点名称", example = "职业道德基础")
    private String certName;

    @Schema(description = "认定点代码", example = "KP001")
    private String certCode;

    @Schema(description = "题干内容", example = "以下哪项是家政服务员的基本职业道德要求？")
    private String title;

    @Schema(description = "题型", example = "单选题")
    private String type;

    @Schema(description = "参考答案", example = "A")
    private String answer;

    @Schema(description = "业务模块", example = "家政业务")
    private String biz;

    @Schema(description = "业务模块名称", example = "家政服务业务")
    private String bizName;

    @Schema(description = "难度等级：1-简单，2-中等，3-困难", example = "1")
    private Integer difficulty;

    @Schema(description = "题目分值", example = "5.00")
    private BigDecimal score;

    @Schema(description = "答题时间限制（秒），0表示无限制", example = "60")
    private Integer timeLimit;

    @Schema(description = "题目解析", example = "家政服务员应当具备良好的职业道德...")
    private String explanation;

    @Schema(description = "关键词，用逗号分隔", example = "职业道德,家政服务")
    private String keywords;

    @Schema(description = "状态：0-禁用，1-启用", example = "1")
    private Integer status;

    @Schema(description = "创建人", example = "admin")
    private String creator;

    @Schema(description = "创建人姓名", example = "管理员")
    private String creatorName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人", example = "admin")
    private String updater;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
