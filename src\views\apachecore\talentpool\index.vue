<template>
  <div class="talent-pool-page">
    <el-card>
      <div class="talent-pool-header">
        <span class="title"
          ><el-icon><UserFilled /></el-icon> 人才库</span
        >
      </div>
      <div class="talent-pool-divider">
        <span class="insight-title"><b>人才洞察中心</b></span>
        <el-button class="create-talent-btn" type="text" @click="onCreate" style="float: right">
          <el-icon style="vertical-align: middle; margin-right: 4px"><Plus /></el-icon>
          <span style="vertical-align: middle">新建人才</span>
        </el-button>
      </div>
      <el-divider style="margin: 8px 0 16px 0" />
      <ContentWrap>
        <el-form
          :inline="true"
          :model="searchForm"
          ref="searchFormRef"
          label-width="80px"
          class="-mb-15px"
        >
          <el-form-item label="关键词">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索姓名/手机/身份证..."
              clearable
              class="!w-240px"
            />
          </el-form-item>
          <el-form-item label="所属机构">
            <el-select v-model="searchForm.orgId" placeholder="请选择所属机构" clearable filterable class="!w-240px">
              <el-option v-for="item in orgOptions" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="用户来源">
            <el-select
              v-model="searchForm.source"
              placeholder="请选择用户来源"
              clearable
              class="!w-240px"
            >
              <el-option label="全部" value="" />
              <el-option label="高校实践小程序" value="高校实践小程序" />
              <el-option label="家政服务员注册" value="家政服务员注册" />
              <el-option label="技能培训报名" value="技能培训报名" />
            </el-select>
          </el-form-item>
          <el-form-item label="用户标签">
            <el-input
              v-model="searchForm.tag"
              placeholder="搜索标签..."
              clearable
              class="!w-240px"
            />
          </el-form-item>
          <el-form-item label="用户状态">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择用户状态"
              clearable
              class="!w-240px"
            >
              <el-option label="全部" value="" />
              <el-option label="正常" value="正常" />
              <el-option label="待合并" value="待合并" />
              <el-option label="已禁用" value="已禁用" />
            </el-select>
          </el-form-item>
          <el-form-item label="平台自营">
            <el-select v-model="searchForm.isSelfSupport" placeholder="请选择" clearable class="!w-240px">
              <el-option label="全部" value="" />
              <el-option label="是" value="1" />
              <el-option label="否" value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch"
              ><el-icon><Search /></el-icon>搜索</el-button
            >
            <el-button @click="onReset"
              ><el-icon><Refresh /></el-icon>重置</el-button
            >
          </el-form-item>
        </el-form>
      </ContentWrap>
      <ContentWrap>
        <el-table
          :data="tableData || []"
          border
          v-loading="loading"
          @selection-change="onSelectionChange"
          empty-text="暂无数据"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="用户信息" min-width="200" align="center">
            <template #default="{ row }">
              <div>
                <b>{{ row.name }}</b> (OneID: {{ row.oneid }})<br />
                <span style="color: #888">{{ row.phone }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="所属机构" min-width="160" align="center">
            <template #default="{ row }">
              <span>{{ row.orgName || '-' }}</span>
            </template>
          </el-table-column>
         
          <el-table-column label="关键标签" min-width="180" align="center">
            <template #default="{ row }">
              <el-tag
                v-for="tag in row.tags"
                :key="tag"
                :type="getTagType(tag)"
                style="margin-right: 4px"
                >{{ tag }}</el-tag
              >
            </template>
          </el-table-column>
          <el-table-column label="档案完整度" min-width="160" align="center">
            <template #default="{ row }">
              <el-progress
                :percentage="row.completeness"
                :color="getProgressColor(row.completeness)"
                :show-text="true"
              />
            </template>
          </el-table-column>
          <el-table-column label="状态" min-width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">{{ row.status }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="180" align="center">
            <template #default="{ row }">
              <el-button size="small" type="primary" link @click="onViewProfile(row)"
                ><el-icon><View /></el-icon>查看画像</el-button
              >
              <el-button
                v-if="row.status === '正常'"
                size="small"
                type="primary"
                link
                @click="onEdit(row)"
                ><el-icon><Edit /></el-icon>编辑</el-button
              >
              <el-button
                v-if="row.status === '正常'"
                size="small"
                type="danger"
                link
                @click="onChangeStatus(row, '停用')"
                ><el-icon><Close /></el-icon>停用</el-button
              >
              <el-button
                v-if="row.status === '停用'"
                size="small"
                type="success"
                link
                @click="onChangeStatus(row, '正常')"
                ><el-icon><Refresh /></el-icon>启用</el-button
              >
              <el-button
                v-if="row.status === '待合并'"
                size="small"
                type="warning"
                link
                @click="onMerge(row)"
                ><el-icon><Refresh /></el-icon>合并</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <Pagination
          :total="total"
          v-model:page="searchForm.pageNo"
          v-model:limit="searchForm.pageSize"
          @pagination="onSearch"
        />
      </ContentWrap>
      <AddTalent v-model="createDialogVisible" :talent="editTalentData" @saved="onAddTalentSaved" />
    </el-card>
    <el-drawer v-model="drawerVisible" title="人才画像详情" size="33%" :with-header="true">
      <TalentDetail v-if="drawerVisible" v-model:userId="selectedUserId" />
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { TalentPoolApi, TalentUser } from '@/api/apachecore/talentpool/index'
import { ElMessage } from 'element-plus'
import { UserFilled, Plus, Search, Refresh, View, Edit, Close } from '@element-plus/icons-vue'
import TalentDetail from '@/views/apachecore/talentpool/TalentDetail.vue'
import AddTalent from '@/views/apachecore/talentpool/components/AddTalent.vue'
import { getSimpleDeptList } from '@/api/system/dept'

const searchForm = reactive({
  keyword: '',
  orgId: '', // 新增
  source: '',
  tag: '',
  status: '',
  pageNo: 1,
  pageSize: 10,
  isSelfSupport: '' // 新增平台自营筛选
})
const tableData = ref<TalentUser[]>([])
const total = ref(0)
const createDialogVisible = ref(false)
const loading = ref(false)
const checkedIds = ref<number[]>([])
const searchFormRef = ref()
const createStep = ref(1)
const editTalentData = ref<TalentUser | null>(null)

const createForm = reactive({
  name: '',
  phone: '',
  identityId: '',
  email: '',
  gender: '男',
  birthday: '',
  tagsInput: '',
  tags: [] as string[]
})
const createFormRef = ref()
const educationForm = reactive({
  school: '',
  major: '',
  degreeType: '',
  rank: '',
  startTime: '',
  endTime: ''
})
const educationFormRef = ref()
const practiceForm = reactive({
  practiceName: '',
  practiceOrg: '',
  practiceStart: '',
  practiceEnd: '',
  practiceSummary: '',
  internCompany: '',
  internJob: '',
  internStart: '',
  internEnd: '',
  internDuty: '',
  projectName: '',
  projectDesc: ''
})
const practiceFormRef = ref()
const recommendTags = [
  '认证:高级母婴护理',
  '认证:金牌月嫂',
  '认证:健康证',
  '认证:教师资格证',
  '技能:催乳',
  '技能:高级收纳',
  '技能:康复保健',
  '技能:Office办公',
  '技能:沟通能力强',
  '经验:育儿早',
  '经验:服务高端社区',
  '经验:2年+工作经验',
  '经验:服务30+家庭',
  '评价:细心',
  '评价:有耐心',
  '评价:守时',
  '学历:博士',
  '学历:硕士',
  '学历:985',
  '学历:本科',
  '学历:大学',
  '身份:学生',
  '身份:认证月嫂',
  '身份:培训学员',
  '身份:求职者',
  '已实名'
]
const trainingForm = reactive({
  org: '',
  course: '',
  finishDate: '',
  skillName: '',
  skillLevel: ''
})
const trainingFormRef = ref()
const certificateForm = reactive({
  certName: '',
  certNo: '',
  certOrg: '',
  issueDate: '',
  expireDate: '',
  source: '平台录入',
  status: '待认证',
  file: null as File | null
})
const certificateFormRef = ref()
const jobForm = reactive({
  company: '',
  position: '',
  startTime: '',
  endTime: '',
  salary: '',
  applyCompany: '',
  applyPosition: '',
  applyDate: '',
  applyStatus: '已投递'
})
const jobFormRef = ref()

const drawerVisible = ref(false)
const selectedTalent = ref<TalentUser | null>(null)
const selectedUserId = ref<string | number | undefined>(undefined)

// 机构下拉数据
const orgOptions = ref<any[]>([])
async function loadOrgOptions() {
  const res = await getSimpleDeptList()
  const topLevels = res.filter((dept: any) => dept.parentId === 0)
  const topLevelMap = Object.fromEntries(topLevels.map((dept: any) => [dept.id, dept.name]))
  const children = res.filter((dept: any) => Object.keys(topLevelMap).map(Number).includes(dept.parentId))
  orgOptions.value = children.map(child => ({ label: `${topLevelMap[child.parentId]}/${child.name}`, id: child.id }))
}
loadOrgOptions()

const onSearch = async () => {
  loading.value = true
  try {
    const { list, total: totalCount } = await TalentPoolApi.getTalentPage({
      keyword: searchForm.keyword,
      orgId: searchForm.orgId as any, // 强制断言，保证能传递
      source: searchForm.source,
      tag: searchForm.tag,
      status: searchForm.status,
      pageNo: searchForm.pageNo,
      pageSize: searchForm.pageSize,
      isSelfSupport: searchForm.isSelfSupport // 新增
    })
    tableData.value = list
    total.value = totalCount
  } finally {
    loading.value = false
  }
}
const onReset = () => {
  searchForm.keyword = ''
  searchForm.orgId = ''
  searchForm.source = ''
  searchForm.tag = ''
  searchForm.status = ''
  searchForm.pageNo = 1
  searchForm.pageSize = 10
  onSearch()
}
const onSelectionChange = (rows: TalentUser[]) => {
  checkedIds.value = rows.map((row) => row.id)
}
const onViewProfile = (row: TalentUser) => {
  selectedTalent.value = row
  selectedUserId.value = row.id
  drawerVisible.value = true
}
const onEdit = (row: TalentUser) => {
  editTalentData.value = { ...row }
  createDialogVisible.value = true
}
const onDisable = async (row: TalentUser) => {
  await TalentPoolApi.disableTalent(row.id)
  ElMessage.success('已停用')
  onSearch()
}
const onMerge = async (row: TalentUser) => {
  await TalentPoolApi.mergeTalent(row.id)
  ElMessage.success('已发起合并')
  onSearch()
}
const onCreate = () => {
  editTalentData.value = null
  createDialogVisible.value = true
}
const getTagType = (tag: string): 'success' | 'info' | 'primary' | 'warning' | 'danger' => {
  if (tag.includes('认证')) return 'success'
  if (tag.includes('学历')) return 'info'
  if (tag.includes('技能')) return 'primary'
  return 'default' as any // fallback
}
const getStatusType = (status: string): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  if (status === '正常') return 'success'
  if (status === '待合并') return 'warning'
  if (status === '已禁用') return 'info'
  return 'default' as any
}
const getProgressColor = (percent: number) => {
  if (percent >= 90) return '#13ce66'
  if (percent >= 60) return '#e6a23c'
  return '#f56c6c'
}
function addTag() {
  const val = createForm.tagsInput.trim()
  if (val && !createForm.tags.includes(val)) {
    createForm.tags.push(val)
  }
  createForm.tagsInput = ''
}
function removeTag(idx: number) {
  createForm.tags.splice(idx, 1)
}
function addRecommendTag(tag: string) {
  if (!createForm.tags.includes(tag)) {
    createForm.tags.push(tag)
  }
}
function nextStep() {
  if (createStep.value < 6) {
    createStep.value++
  }
}
function prevStep() {
  if (createStep.value > 1) {
    createStep.value--
  }
}
function disabledBirthdayDate(date: Date) {
  return date.getTime() >= Date.now()
}
function handleCertFileChange(file) {
  certificateForm.file = file.raw
}
function onAddTalentSaved() {
  // ElMessage.success('新增成功，已刷新列表！')
  onSearch()
}
const onChangeStatus = async (row: TalentUser, targetStatus: string) => {
  try {
    await TalentPoolApi.changeTalentStatus(row.id, targetStatus)
    ElMessage.success(targetStatus === '正常' ? '已启用' : '已停用')
    onSearch()
  } catch (e) {
    ElMessage.error('操作失败')
  }
}
onMounted(onSearch)
</script>

<style scoped>
.talent-pool-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.title {
  font-size: 22px;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 8px;
}
.talent-pool-divider {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
  margin-bottom: 0;
}
.insight-title {
  font-size: 16px;
  font-weight: bold;
}
.create-talent-btn {
  cursor: pointer;
  user-select: none;
  font-size: 15px;
  color: #409eff;
  display: flex;
  align-items: center;
}
.create-talent-btn:hover {
  color: #66b1ff;
}
.talent-pool-search {
  margin-bottom: 8px;
}
.talent-create-step {
  margin-bottom: 24px;
}
.talent-create-form {
  margin-top: 10px;
}
.section-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 16px;
}
.talent-form-grid .el-form-item {
  margin-bottom: 18px;
}
.talent-tags-list {
  margin-top: 6px;
}
.recommend-title {
  font-weight: bold;
  margin-bottom: 6px;
  margin-top: 10px;
}
.recommend-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px 8px;
}
.recommend-tag {
  cursor: pointer;
  margin-bottom: 6px;
  user-select: none;
}
.recommend-tag:hover {
  background: #f0f9eb;
  color: #67c23a;
}
</style>
