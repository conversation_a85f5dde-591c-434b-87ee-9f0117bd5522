<!--
  页面名称：统一师资库
  功能描述：展示讲师统计、筛选、列表，支持搜索、导入、导出等操作
-->
<template>
  <div class="teacher-database">
    <!-- 顶部统计卡片 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card stat-bg1">
          <div class="stat-card-inner">
            <el-icon class="stat-icon stat-icon1"><i class="el-icon-user"></i></el-icon>
            <div>
              <div class="stat-num">{{ stat.total }}</div>
              <div class="stat-label">讲师总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card stat-bg2">
          <div class="stat-card-inner">
            <el-icon class="stat-icon stat-icon2"><i class="el-icon-user"></i></el-icon>
            <div>
              <div class="stat-num">{{ stat.inner }}</div>
              <div class="stat-label">内部讲师</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card stat-bg3">
          <div class="stat-card-inner">
            <el-icon class="stat-icon stat-icon3"><i class="el-icon-user"></i></el-icon>
            <div>
              <div class="stat-num">{{ stat.outer }}</div>
              <div class="stat-label">外部讲师</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card stat-bg4">
          <div class="stat-card-inner">
            <el-icon class="stat-icon stat-icon4"><i class="el-icon-document"></i></el-icon>
            <div>
              <div class="stat-num">{{ stat.pending }}</div>
              <div class="stat-label">待签约</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 筛选表单 -->
    <el-form :inline="true" :model="searchForm" class="mb-2" @submit.prevent>
      <el-form-item label="讲师类型：">
        <el-select v-model="searchForm.type" placeholder="全部" clearable style="width: 120px">
          <el-option label="全部" value="" />
          <el-option label="内部讲师" value="内部讲师" />
          <el-option label="外部讲师" value="外部讲师" />
        </el-select>
      </el-form-item>
      <el-form-item label="业务模块：">
        <el-select v-model="searchForm.biz" placeholder="全部" clearable style="width: 120px">
          <el-option label="全部" value="" />
          <el-option
            v-for="item in businessModuleOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="合作状态：">
        <el-select v-model="searchForm.status" placeholder="全部" clearable style="width: 120px">
          <el-option label="全部" value="" />
          <el-option label="合作中" value="合作中" />
          <el-option label="待沟通" value="待沟通" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="searchForm.keyword"
          placeholder="搜索姓名/擅长领域/资质"
          clearable
          style="width: 220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">筛选</el-button>
        <el-button icon="el-icon-download" @click="downloadTemplate">下载模板</el-button>
        <el-button type="primary" icon="el-icon-upload" @click="showBatchImport"
          >批量导入</el-button
        >
      </el-form-item>
    </el-form>

    <!-- 讲师表格 -->
    <div class="table-responsive">
      <el-table
        :data="tableData"
        border
        style="min-width: 900px; width: 100%"
        :header-cell-style="{ background: '#fafbfc' }"
      >
        <el-table-column label="讲师姓名" min-width="200">
          <template #default="scope">
            <div class="teacher-info">
              <el-avatar :src="scope.row.avatar" :size="40" />
              <div class="teacher-meta">
                <div class="teacher-name" :title="scope.row.name">{{ scope.row.name }}</div>
                <div class="teacher-desc ellipsis" :title="scope.row.description">{{
                  scope.row.description || '暂无简介'
                }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="讲师类型" min-width="90" />
        <el-table-column prop="biz" label="业务模块" min-width="90" />
        <el-table-column prop="org" label="关联机构" min-width="120" />
        <el-table-column prop="field" label="擅长领域" min-width="120" />
        <el-table-column prop="phone" label="联系电话" min-width="120" />
        <el-table-column label="资质名称" min-width="90">
          <template #default="scope">
            <span v-if="scope.row.certFiles && scope.row.certFiles.length > 0">
              {{ scope.row.certFiles[0].certName }}
            </span>
            <span v-else>暂无资质</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="合作状态" min-width="90">
          <template #default="scope">
            <el-tag v-if="scope.row.status === '合作中'" type="success">合作中</el-tag>
            <el-tag v-else-if="scope.row.status === '待沟通'" type="warning">待沟通</el-tag>
            <el-tag v-else type="info">{{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="signStatus" label="电子签约状态" min-width="120">
          <template #default="scope">
            <el-tag v-if="scope.row.signStatus === '已签约'" type="success">已签约</el-tag>
            <el-tag v-else-if="scope.row.signStatus === '签约中'" type="warning">签约中</el-tag>
            <el-tag v-else-if="scope.row.signStatus === '未签约'" type="info">未签约</el-tag>
            <el-tag v-else type="info">{{ scope.row.signStatus }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="90" fixed="right">
          <template #default="scope">
            <el-dropdown @command="handleCommand(scope.row, $event)">
              <el-button size="small" type="primary">
                操作 <el-icon class="el-icon--right"><i class="el-icon-arrow-down"></i></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit">编辑</el-dropdown-item>
                  <el-dropdown-item command="view">查看</el-dropdown-item>
                  <el-dropdown-item command="delete" style="color: #ff4d4f">删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <el-pagination
        v-if="pagination.total > 0"
        background
        layout="total, sizes, prev, pager, next"
        :total="pagination.total"
        :current-page="pagination.pageNo"
        :page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        @current-change="handlePageChange"
        @size-change="handlePageSizeChange"
      />
    </div>

    <!-- 新增/编辑师资抽屉 -->
    <AddTeacher
      v-model:visible="addTeacherVisible"
      v-model:editData="editTeacherData"
      @close="onAddTeacherClose"
    />
    <!-- 师资详情抽屉 -->
    <TeacherDetail
      :visible="detailVisible"
      :detail-data="detailData"
      @close="onDetailClose"
      @edit="onDetailEdit"
    />
    <!-- 批量导入弹窗 -->
    <BatchImportTeacher v-model:visible="batchImportVisible" @close="onBatchImportClose" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import AddTeacher from './components/AddTeacher.vue'
import TeacherDetail from './components/TeacherDetail.vue'
import BatchImportTeacher from './components/BatchImportTeacher.vue'
import { getDictDataPage } from '@/api/system/dict/dict.data'
import { getTeacherStat, getTeacherPage, getTeacherDetail, deleteTeacher } from '@/api/infra/teacher'

/** 顶部统计数据 */
const stat = reactive({
  total: 0,
  inner: 0,
  outer: 0,
  pending: 0
})

/** 搜索表单数据 */
const searchForm = reactive({
  type: '',
  biz: '',
  status: '',
  keyword: ''
})

/** 表格数据 */
const tableData = ref<any[]>([])
const pagination = reactive({ pageNo: 1, pageSize: 10, total: 0 })

const addTeacherVisible = ref(false)
const editTeacherData = ref<any>(null)
const detailVisible = ref(false)
const detailData = ref<any>(null)
const batchImportVisible = ref(false)
const businessModuleOptions = ref<any[]>([])

const onAddTeacherClose = async (isSave: boolean) => {
  addTeacherVisible.value = false
  if (isSave) {
    await refreshData()
  }
}
const onDetailClose = () => {
  detailVisible.value = false
}
const onDetailEdit = () => {
  detailVisible.value = false
  onEdit(detailData.value)
}

// 显示批量导入弹窗
const showBatchImport = () => {
  batchImportVisible.value = true
}

// 批量导入关闭处理
const onBatchImportClose = async (isSuccess: boolean) => {
  batchImportVisible.value = false
  if (isSuccess) {
    await refreshData()
  }
}

/** 获取业务模块字典数据 */
const fetchBusinessModuleOptions = async () => {
  try {
    const res = await getDictDataPage({ dictType: 'business_module', pageNo: 1, pageSize: 100 })
    businessModuleOptions.value = (res?.list || []).map((item) => ({
      label: item.label,
      value: item.value
    }))
  } catch (error) {
    console.error('获取业务模块字典数据失败:', error)
  }
}

/** 获取讲师统计卡片数据 */
const fetchStat = async () => {
  try {
    const res = await getTeacherStat()
    if (res) {
      stat.total = res.total || 0
      stat.inner = res.inner || 0
      stat.outer = res.outer || 0
      stat.pending = res.pending || 0
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

/** 获取讲师列表（分页） */
const fetchList = async () => {
  const params = {
    pageNo: pagination.pageNo,
    pageSize: pagination.pageSize,
    type: searchForm.type,
    biz: searchForm.biz,
    status: searchForm.status,
    keyword: searchForm.keyword
  }
  const res = await getTeacherPage(params)
  if (res && res.list) {
    tableData.value = res.list || []
    pagination.total = res.total || 0
  } else {
    tableData.value = []
    pagination.total = 0
  }
}

/** 刷新列表和统计数据 */
const refreshData = async () => {
  await Promise.all([fetchList(), fetchStat()])
}

/** 搜索按钮操作 */
const handleSearch = () => {
  pagination.pageNo = 1
  fetchList()
  // 搜索时不需要更新统计卡片，因为统计卡片显示的是总体数据
}

const downloadTemplate = () => {
  const link = document.createElement('a')
  link.href = '/templates/师资库导入模板.csv'
  link.download = '师资库导入模板.csv'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

const onEdit = async (row: any) => {
  try {
    // 获取讲师详情数据
    const res = await getTeacherDetail(row.id)
    if (res) {
      editTeacherData.value = res
      addTeacherVisible.value = true
    } else {
      ElMessage.error('获取讲师详情失败')
    }
  } catch (error) {
    console.error('获取讲师详情失败:', error)
    ElMessage.error('获取讲师详情失败')
  }
}
const onViewDetail = async (row: any) => {
  try {
    const res = await getTeacherDetail(row.id)

    // API直接返回数据对象，不是标准格式
    if (res && res.id) {
      detailData.value = res
    } else {
      ElMessage.error('获取讲师详情失败：响应格式错误')
      return
    }

    detailVisible.value = true
  } catch (error) {
    console.error('获取讲师详情失败:', error)
    ElMessage.error('获取讲师详情失败')
  }
}

const onDelete = async (row: any) => {
  await deleteTeacher(row.id)
  await refreshData()
}

function handleCommand(row: any, command: string) {
  if (command === 'edit') onEdit(row)
  else if (command === 'view') onViewDetail(row)
  else if (command === 'delete') onDelete(row)
}

function handlePageChange(page: number) {
  pagination.pageNo = page
  fetchList()
}
function handlePageSizeChange(size: number) {
  pagination.pageSize = size
  fetchList()
}

onMounted(() => {
  fetchBusinessModuleOptions()
  fetchStat()
  fetchList()
})
</script>

<style scoped lang="scss">
.teacher-database {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}
.teacher-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}
.stat-card {
  border: none;
  box-shadow: 0 2px 8px #f0f1f2;
  border-radius: 8px;
  padding: 0;
}
.stat-card-inner {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 0 8px 8px;
}
.stat-icon {
  font-size: 24px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
}
.stat-num {
  font-size: 18px;
  font-weight: bold;
}
.stat-label {
  color: #888;
  font-size: 12px;
}
.stat-icon1 {
  color: #409eff;
  background: #e6f1ff;
}
.stat-icon2 {
  color: #67c23a;
  background: #e8f7e0;
}
.stat-icon3 {
  color: #e6a23c;
  background: #fff5e6;
}
.stat-icon4 {
  color: #409eff;
  background: #e6f1ff;
}
.stat-bg1 {
  background: linear-gradient(90deg, #e6f1ff 0%, #cce3ff 100%);
}
.stat-bg2 {
  background: linear-gradient(90deg, #e8f7e0 0%, #d2f2c2 100%);
}
.stat-bg3 {
  background: linear-gradient(90deg, #fff5e6 0%, #ffe1b8 100%);
}
.stat-bg4 {
  background: linear-gradient(90deg, #e6f1ff 0%, #cce3ff 100%);
}
.mb-4 {
  margin-bottom: 24px;
}
.mb-2 {
  margin-bottom: 12px;
}
.teacher-info {
  display: flex;
  align-items: center;
  gap: 12px;
}
.teacher-meta {
  display: flex;
  flex-direction: column;
  max-width: 160px;
}
.teacher-name {
  font-weight: bold;
  font-size: 16px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  cursor: pointer;
}
.teacher-desc {
  color: #888;
  font-size: 13px;
  margin-top: 2px;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}
.table-responsive {
  width: 100%;
  overflow-x: auto;
}
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}
@media (max-width: 1200px) {
  .stat-card-inner {
    gap: 8px;
  }
  .stat-num {
    font-size: 15px;
  }
  .stat-label {
    font-size: 11px;
  }
  .el-table th,
  .el-table td {
    font-size: 13px;
  }
}
@media (max-width: 900px) {
  .stat-card-inner {
    gap: 4px;
  }
  .stat-num {
    font-size: 13px;
  }
  .stat-label {
    font-size: 10px;
  }
  .el-table th,
  .el-table td {
    font-size: 12px;
  }
  .table-responsive {
    min-width: 600px;
  }
}
</style>
