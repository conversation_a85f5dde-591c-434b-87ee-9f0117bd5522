## 新增图文

**接口地址**:`/system/material/news/create`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
{
  "id": 1,
  "name": "",
  "categoryId": 0,
  "mediaId": "",
  "content": "",
  "thumbUrl": "",
  "articleCount": 0,
  "sourceType": 0,
  "sourceOrgId": 0,
  "sourceOrgName": "",
  "accountId": 0,
  "status": 0,
  "publishTime": "",
  "tags": "",
  "description": "",
  "isPermanent": true,
  "expireTime": "",
  "newsType": 0,
  "visibleOrgId": 0,
  "visibleOrgName": "",
  "tenantId": 0,
  "creator": "",
  "createTime": "",
  "updater": "",
  "updateTime": "",
  "deleted": true
}
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| newsSaveReqVO | 管理后台 - 图文素材新增/更新 Request VO | body | true | NewsSaveReqVO | NewsSaveReqVO |
| &emsp;&emsp;id | 图文ID |  | false | integer(int64) |  |
| &emsp;&emsp;name | 图文名称 |  | true | string |  |
| &emsp;&emsp;categoryId | 分类ID，关联mp_material_category.id |  | false | integer(int64) |  |
| &emsp;&emsp;mediaId | 微信媒体ID |  | false | string |  |
| &emsp;&emsp;content | 图文内容，文案内容 |  | false | string |  |
| &emsp;&emsp;thumbUrl | 缩略图URL |  | false | string |  |
| &emsp;&emsp;articleCount | 文章数量 |  | false | integer(int32) |  |
| &emsp;&emsp;sourceType | 素材来源类型，1-本地上传，2-微信同步，3-外部链接 |  | false | integer(int32) |  |
| &emsp;&emsp;sourceOrgId | 来源机构ID |  | true | integer(int64) |  |
| &emsp;&emsp;sourceOrgName | 来源机构名称 |  | true | string |  |
| &emsp;&emsp;accountId | 公众号账号ID |  | false | integer(int64) |  |
| &emsp;&emsp;status | 状态，0-草稿，1-已发布，2-已下线 |  | false | integer(int32) |  |
| &emsp;&emsp;publishTime | 发布时间 |  | false | string(date-time) |  |
| &emsp;&emsp;tags | 标签，多个标签用逗号分隔 |  | false | string |  |
| &emsp;&emsp;description | 图文描述 |  | false | string |  |
| &emsp;&emsp;isPermanent | 是否永久素材，0-临时，1-永久 |  | false | boolean |  |
| &emsp;&emsp;expireTime | 过期时间（临时素材） |  | false | string(date-time) |  |
| &emsp;&emsp;newsType | 图文类型，1-已发布，2-草稿 |  | false | integer(int32) |  |
| &emsp;&emsp;visibleOrgId | 可视范围机构ID |  | false | integer(int64) |  |
| &emsp;&emsp;visibleOrgName | 可视范围机构名称 |  | false | string |  |
| &emsp;&emsp;tenantId | 租户ID |  | false | integer(int64) |  |
| &emsp;&emsp;creator | 创建人 |  | false | string |  |
| &emsp;&emsp;createTime | 创建时间 |  | false | string(date-time) |  |
| &emsp;&emsp;updater | 更新人 |  | false | string |  |
| &emsp;&emsp;updateTime | 更新时间 |  | false | string(date-time) |  |
| &emsp;&emsp;deleted | 是否删除，0-未删除，1-已删除 |  | false | boolean |  |

**响应状态**:

| 状态码 | 说明 | schema           |
| ------ | ---- | ---------------- |
| 200    | OK   | CommonResultLong |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | integer(int64) | integer(int64) |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": 0,
	"msg": ""
}
```

## 删除图文

**接口地址**:`/system/material/news/delete`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型       | schema |
| -------- | -------- | -------- | -------- | -------------- | ------ |
| id       |          | query    | true     | integer(int64) |        |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 图文列表

**接口地址**:`/system/material/news/list`

**请求方式**:`GET`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称       | 参数说明               | 请求类型 | 是否必须 | 数据类型 | schema |
| -------------- | ---------------------- | -------- | -------- | -------- | ------ |
| pageNo         | 页码，从 1 开始        | query    | true     | string   |        |
| pageSize       | 每页条数，最大值为 100 | query    | true     | string   |        |
| name           | 图文名称，模糊搜索     | query    | false    | string   |        |
| sourceOrgId    | 来源机构ID             | query    | false    | string   |        |
| categoryId     | 分类ID                 | query    | false    | string   |        |
| visibleOrgId   | 可视范围机构ID         | query    | false    | string   |        |
| visibleOrgName | 可视范围机构名称       | query    | false    | string   |        |

**响应状态**:

| 状态码 | 说明 | schema                           |
| ------ | ---- | -------------------------------- |
| 200    | OK   | CommonResultPageResultNewsRespVO |

**响应参数**:

| 参数名称 | 参数说明 | 类型 | schema |
| --- | --- | --- | --- |
| code |  | integer(int32) | integer(int32) |
| data |  | PageResultNewsRespVO | PageResultNewsRespVO |
| &emsp;&emsp;list | 数据 | array | NewsRespVO |
| &emsp;&emsp;&emsp;&emsp;id | 图文ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;name | 图文名称 | string |  |
| &emsp;&emsp;&emsp;&emsp;categoryId | 分类ID，关联mp_material_category.id | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;mediaId | 微信媒体ID | string |  |
| &emsp;&emsp;&emsp;&emsp;content | 图文内容，文案内容 | string |  |
| &emsp;&emsp;&emsp;&emsp;thumbUrl | 缩略图URL | string |  |
| &emsp;&emsp;&emsp;&emsp;articleCount | 文章数量 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;sourceType | 素材来源类型，1-本地上传，2-微信同步，3-外部链接 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;sourceOrgId | 来源机构ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;sourceOrgName | 来源机构名称 | string |  |
| &emsp;&emsp;&emsp;&emsp;accountId | 公众号账号ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;status | 状态，0-草稿，1-已发布，2-已下线 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;publishTime | 发布时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;tags | 标签，多个标签用逗号分隔 | string |  |
| &emsp;&emsp;&emsp;&emsp;description | 图文描述 | string |  |
| &emsp;&emsp;&emsp;&emsp;isPermanent | 是否永久素材，0-临时，1-永久 | boolean |  |
| &emsp;&emsp;&emsp;&emsp;expireTime | 过期时间（临时素材） | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;newsType | 图文类型，1-已发布，2-草稿 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;visibleOrgId | 可视范围机构ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;visibleOrgName | 可视范围机构名称 | string |  |
| &emsp;&emsp;&emsp;&emsp;tenantId | 租户ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;creator | 创建人 | string |  |
| &emsp;&emsp;&emsp;&emsp;createTime | 创建时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;updater | 更新人 | string |  |
| &emsp;&emsp;&emsp;&emsp;updateTime | 更新时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;deleted | 是否删除，0-未删除，1-已删除 | boolean |  |
| &emsp;&emsp;total | 总量 | integer(int64) |  |
| msg |  | string |  |

**响应示例**:

```javascript
{
	"code": 0,
	"data": {
		"list": [
			{
				"id": 1,
				"name": "示例图文",
				"categoryId": 0,
				"mediaId": "",
				"content": "",
				"thumbUrl": "",
				"articleCount": 0,
				"sourceType": 0,
				"sourceOrgId": 1,
				"sourceOrgName": "内部素材库",
				"accountId": 0,
				"status": 0,
				"publishTime": "",
				"tags": "",
				"description": "",
				"isPermanent": true,
				"expireTime": "",
				"newsType": 0,
				"visibleOrgId": 0,
				"visibleOrgName": "",
				"tenantId": 0,
				"creator": "",
				"createTime": "",
				"updater": "",
				"updateTime": "",
				"deleted": true
			}
		],
		"total": 0
	},
	"msg": ""
}
```

## 编辑图文

**接口地址**:`/system/material/news/update`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
{
  "id": 1,
  "name": "",
  "categoryId": 0,
  "mediaId": "",
  "content": "",
  "thumbUrl": "",
  "articleCount": 0,
  "sourceType": 0,
  "sourceOrgId": 0,
  "sourceOrgName": "",
  "accountId": 0,
  "status": 0,
  "publishTime": "",
  "tags": "",
  "description": "",
  "isPermanent": true,
  "expireTime": "",
  "newsType": 0,
  "visibleOrgId": 0,
  "visibleOrgName": "",
  "tenantId": 0,
  "creator": "",
  "createTime": "",
  "updater": "",
  "updateTime": "",
  "deleted": true
}
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| newsSaveReqVO | 管理后台 - 图文素材新增/更新 Request VO | body | true | NewsSaveReqVO | NewsSaveReqVO |
| &emsp;&emsp;id | 图文ID |  | false | integer(int64) |  |
| &emsp;&emsp;name | 图文名称 |  | true | string |  |
| &emsp;&emsp;categoryId | 分类ID，关联mp_material_category.id |  | false | integer(int64) |  |
| &emsp;&emsp;mediaId | 微信媒体ID |  | false | string |  |
| &emsp;&emsp;content | 图文内容，文案内容 |  | false | string |  |
| &emsp;&emsp;thumbUrl | 缩略图URL |  | false | string |  |
| &emsp;&emsp;articleCount | 文章数量 |  | false | integer(int32) |  |
| &emsp;&emsp;sourceType | 素材来源类型，1-本地上传，2-微信同步，3-外部链接 |  | false | integer(int32) |  |
| &emsp;&emsp;sourceOrgId | 来源机构ID |  | true | integer(int64) |  |
| &emsp;&emsp;sourceOrgName | 来源机构名称 |  | true | string |  |
| &emsp;&emsp;accountId | 公众号账号ID |  | false | integer(int64) |  |
| &emsp;&emsp;status | 状态，0-草稿，1-已发布，2-已下线 |  | false | integer(int32) |  |
| &emsp;&emsp;publishTime | 发布时间 |  | false | string(date-time) |  |
| &emsp;&emsp;tags | 标签，多个标签用逗号分隔 |  | false | string |  |
| &emsp;&emsp;description | 图文描述 |  | false | string |  |
| &emsp;&emsp;isPermanent | 是否永久素材，0-临时，1-永久 |  | false | boolean |  |
| &emsp;&emsp;expireTime | 过期时间（临时素材） |  | false | string(date-time) |  |
| &emsp;&emsp;newsType | 图文类型，1-已发布，2-草稿 |  | false | integer(int32) |  |
| &emsp;&emsp;visibleOrgId | 可视范围机构ID |  | false | integer(int64) |  |
| &emsp;&emsp;visibleOrgName | 可视范围机构名称 |  | false | string |  |
| &emsp;&emsp;tenantId | 租户ID |  | false | integer(int64) |  |
| &emsp;&emsp;creator | 创建人 |  | false | string |  |
| &emsp;&emsp;createTime | 创建时间 |  | false | string(date-time) |  |
| &emsp;&emsp;updater | 更新人 |  | false | string |  |
| &emsp;&emsp;updateTime | 更新时间 |  | false | string(date-time) |  |
| &emsp;&emsp;deleted | 是否删除，0-未删除，1-已删除 |  | false | boolean |  |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```
