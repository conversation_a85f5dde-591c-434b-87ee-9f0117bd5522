----生成数据库表 @/employment @/employment请根据就业服务-就业管理-旗下阿姨页面相关元素并结合以上截图内容生成相关数据库表脚本语句并将将本存放在D:\WorkSpace\JavaCode\CNT-HRSP\chuanneng-hr-service-vue\提示词\业务模块\就业服务的 @数据库表创建脚本.sql 下。

生成后端接口提示词文档 @/employment @/employment @数据库表创建脚本.sql请结合截图中的内容并参照@接口文档生成提示词.md 针对旗下阿姨板块页面相关功能帮我整理一份接口文档生成的提示词，便于我用于生成后端接口相关代码。页面中涉及了列表查询、导出列表、新增阿姨、编辑阿姨信息、查看阿姨信息、生成简历海报以及解约等相关功能。要结合@数据库表创建脚本.sql 中 阿姨管理相关记录表进行。

注意：此处需要重新生成一份旗下阿姨相关的接口文档生成提示词，不要和其他内容混在一起，帮我重新生成一份提示词文件。

生成后端接口代码相关提示词（需要用cursor工具打开后端代码操作）请结合相关@旗下阿姨接口文档生成提示词.md文档 以及@数据库表创建脚本.sql 中旗下阿姨相关表生成后端相关业务代码，要完善所有业务逻辑，对于新增、修改、删除等变更数据的操作都需要接入操作日志相关记录。

注意：此处删除不要做物理删除，要做逻辑删除。

@/bztmaster-module-system-server我check代码发现，对应的旗下阿姨模块中对应阿姨列表的导出以及生成阿姨建立海报的功能的业务逻辑没有实现。1、导出阿姨列表数据请按照 阿姨姓名、综合评级、服务类型、累计单数、当前状态、平台状态这几列进行导出。2、生成简历海报就请按照阿姨详情的相关信息导出生成一份简历形式的PDF文件。

相关字典：

服务类型:service_type
旗下阿姨-平台状态：platform_status
综合评级:composite_rating 1.0-1.99 1星2.0-2.99 2星3.0-3.99 3星4.0-4.99 4星5 5星

---

- //处理实际接口绑定

@/employment @/employment 1、请帮我将旗下阿姨模块列表、新增以及编辑页中的下拉框更换成通过实际接口调用，对应的调用方式以及接口文档请参照@基础数据字典接口文档.md对应数据字典类型：

2、请帮我将列表中的查询、新增、编辑、解约以及查看详情等操作的功能更换成实际的接口对接，具体对接方式请参照@旗下阿姨管理详细接口文档.md
