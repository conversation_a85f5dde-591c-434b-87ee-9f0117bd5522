package cn.bztmaster.cnt.module.publicbiz.controller.admin.question;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.*;
import cn.bztmaster.cnt.module.publicbiz.service.question.QuestionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 考题管理 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/publicbiz/question")
@Tag(name = "资源中心-考题管理")
public class QuestionController {

    @Resource
    private QuestionService questionService;

    @PostMapping("/create")
    @Operation(summary = "新增考题")
    public CommonResult<Long> createQuestion(@Valid @RequestBody QuestionSaveReqVO createReqVO) {
        return CommonResult.success(questionService.createQuestion(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "编辑考题")
    public CommonResult<Boolean> updateQuestion(@Valid @RequestBody QuestionSaveReqVO updateReqVO) {
        questionService.updateQuestion(updateReqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除考题")
    public CommonResult<Boolean> deleteQuestion(@RequestParam("id") Long id) {
        questionService.deleteQuestion(id);
        return CommonResult.success(true);
    }

    @GetMapping("/detail")
    @Operation(summary = "考题详情")
    public CommonResult<QuestionRespVO> getQuestion(@RequestParam("id") Long id) {
        QuestionRespVO question = questionService.getQuestion(id);
        return CommonResult.success(question);
    }

    @GetMapping("/page")
    @Operation(summary = "考题分页查询")
    public CommonResult<PageResult<QuestionRespVO>> getQuestionPage(@Valid QuestionPageReqVO pageReqVO) {
        PageResult<QuestionRespVO> pageResult = questionService.getQuestionPage(pageReqVO);
        return CommonResult.success(pageResult);
    }
}
