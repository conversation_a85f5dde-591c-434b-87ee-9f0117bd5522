package cn.bztmaster.cnt.module.publicbiz.service.question;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.QuestionCategoryListReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.QuestionCategoryRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.QuestionCategorySaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionCategoryDO;

import java.util.Collection;
import java.util.List;

/**
 * 考题分类管理 Service 接口
 *
 * <AUTHOR>
 */
public interface QuestionCategoryService {

    /**
     * 创建考题分类
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createQuestionCategory(QuestionCategorySaveReqVO createReqVO);

    /**
     * 更新考题分类
     *
     * @param updateReqVO 更新信息
     */
    void updateQuestionCategory(QuestionCategorySaveReqVO updateReqVO);

    /**
     * 删除考题分类
     *
     * @param id 编号
     */
    void deleteQuestionCategory(Long id);

    /**
     * 获得考题分类
     *
     * @param id 编号
     * @return 考题分类
     */
    QuestionCategoryRespVO getQuestionCategory(Long id);

    /**
     * 获得考题分类列表
     *
     * @param listReqVO 列表查询
     * @return 考题分类列表
     */
    List<QuestionCategoryRespVO> getQuestionCategoryList(QuestionCategoryListReqVO listReqVO);

    // ==================== API 接口需要的方法 ====================

    /**
     * 获得考题分类 DO
     *
     * @param id 编号
     * @return 考题分类 DO
     */
    QuestionCategoryDO getQuestionCategoryDO(Long id);

    /**
     * 获得考题分类 DO 列表
     *
     * @param ids 编号
     * @return 考题分类 DO 列表
     */
    List<QuestionCategoryDO> getQuestionCategoryDOList(Collection<Long> ids);
}
