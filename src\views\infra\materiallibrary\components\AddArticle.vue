<template>
  <el-dialog v-model="visible" :title="dialogTitle" width="600px" :close-on-click-modal="false">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="80px">
      <el-form-item label="可见范围" prop="visibleOrgId" required>
        <div class="dept-tree-container">
          <el-input
            v-model="deptSearchKeyword"
            placeholder="请输入机构名称搜索"
            clearable
            style="margin-bottom: 10px"
          >
            <template #prefix>
              <el-icon><Plus /></el-icon>
            </template>
          </el-input>
          <div class="dept-tree-wrapper">
            <el-tree
              ref="deptTreeRef"
              :data="deptList"
              :expand-on-click-node="false"
              :filter-node-method="filterDeptNode"
              :props="{ label: 'name', children: 'children' }"
              default-expand-all
              highlight-current
              node-key="id"
              @node-click="handleDeptNodeClick"
              :filter-text="deptSearchKeyword"
              style="max-height: 200px; overflow-y: auto; border: 1px solid #dcdfe6; border-radius: 4px; padding: 8px;"
            >
              <template #default="{ node, data }">
                <span :class="{ 'selected-dept': form.visibleOrgId === data.id }">
                  {{ node.label }}
                </span>
              </template>
            </el-tree>
          </div>
          <div v-if="form.visibleOrgId" class="selected-dept-info">
            <el-tag type="primary" closable @close="clearDept">
              已选择: {{ form.visibleOrgName }}
            </el-tag>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="文章标题" prop="name" required>
        <el-input v-model="form.name" placeholder="请输入文章标题" />
      </el-form-item>
      <el-form-item label="上传图片" prop="coverUrl" required>
        <el-upload
          class="upload-demo"
          list-type="picture-card"
          :http-request="handleImageUploadRequest"
          :show-file-list="true"
          :file-list="imgFileList"
          :limit="1"
          :on-remove="handleRemoveImage"
          accept="image/*"
        >
          <template #default>
            <el-icon><Plus /></el-icon>
          </template>
        </el-upload>
      </el-form-item>
      <el-form-item label="文章内容" prop="content" required>
        <Editor v-model:modelValue="form.content" style="min-height: 200px" />
      </el-form-item>
      <el-form-item label="分组" prop="group" required>
        <el-select v-model="form.group" placeholder="请选择分组" :disabled="!form.visibleOrgId" @focus="handleGroupFocus">
          <el-option v-if="!form.visibleOrgId" label="请先选择可见范围" value="" disabled />
          <el-option v-for="item in groupOptions" :key="item.id" :label="item.name" :value="item.id" />
          <el-option v-if="form.visibleOrgId && groupOptions.length === 0" label="暂无可用分组" value="" disabled />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleOk">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
// 新增：引入文章相关接口
import { createArticle, updateArticle, getArticleDetail } from '@/api/infra/materiallibrary/materialArticle'
import { Editor } from '@/components/Editor'
import request from '@/config/axios'
import { getSimpleDeptList } from '@/api/system/dept'
import { getCategoryList } from '@/api/infra/materiallibrary/materialCategory'

const visible = ref(false)
const formRef = ref()
const form = ref({
  scope: '',
  name: '',
  img: null,
  coverUrl: '',
  content: '',
  group: '',
  id: undefined, // 用于编辑时传递id
  visibleOrgId: undefined, // 新增：可见范围ID
  visibleOrgName: undefined // 新增：可见范围名称
})
const isEdit = ref(false)
const dialogTitle = computed(() => (isEdit.value ? '编辑文章' : '新增文章'))
const rules = {
  scope: [{ required: true, message: '请选择可见范围', trigger: 'change' }],
  name: [{ required: true, message: '请输入文章标题', trigger: 'blur' }],
  coverUrl: [{ required: true, message: '请上传图片', trigger: 'change' }],
  content: [{ required: true, message: '请输入文章内容', trigger: 'blur' }],
  group: [{ required: true, message: '请选择分组', trigger: 'change' }]
}
const imgFileList = ref<any[]>([])

// 新增/编辑弹窗打开
async function open(data?: any) {
  if (data && data.id) {
    isEdit.value = true
    resetForm()
    // 1. 获取后端最新详情
    const res = await getArticleDetail(data.id)
    const detail = res.data || res
    // 2. 赋值可见范围
    form.value.visibleOrgId = detail.visibleOrgId
    form.value.visibleOrgName = detail.visibleOrgName
    await loadDeptList()
    // 3. 加载分组
    if (detail.visibleOrgId) {
      await loadGroupOptions(detail.visibleOrgId)
    }
    // 4. 赋值分组
    form.value.group = detail.categoryId || ''
    // 5. 其它字段
    form.value.id = detail.id
    form.value.name = detail.title || detail.name || ''
    form.value.content = detail.content || ''
    const coverUrl = detail.coverUrl || detail.imgUrl || detail.img || ''
    form.value.coverUrl = coverUrl
    imgFileList.value = coverUrl
      ? [{ name: detail.title || detail.name, url: coverUrl, status: 'done', uid: detail.id || Date.now().toString() }]
      : []
    // 6. 高亮树节点
    nextTick(() => {
      deptTreeRef.value?.setCurrentKey(form.value.visibleOrgId)
    })
  } else {
    isEdit.value = false
    resetForm()
    imgFileList.value = []
  }
  visible.value = true
}
function handleCancel() {
  visible.value = false
}
// 新增/编辑文章，表单校验通过后调接口
async function handleOk() {
  formRef.value.validate(async (valid: boolean) => {
    if (!valid) return
    try {
      const groupObj = groupOptions.value.find(item => item.id === form.value.group)
      const payload = {
        title: form.value.name,
        coverUrl: form.value.coverUrl,
        content: form.value.content,
        categoryId: form.value.group ? Number(form.value.group) : undefined,
        groupName: groupObj ? groupObj.name : '',
        visibleOrgId: form.value.visibleOrgId,
        visibleOrgName: form.value.visibleOrgName,
        sourceOrgId: 1, // 示例，实际应根据业务传递
        sourceOrgName: '内部素材库', // 示例
        description: '',
      }
      if (isEdit.value && form.value.id) {
        // 编辑
        await updateArticle({ ...payload, id: Number(form.value.id) })
        ElMessage.success('编辑成功')
      } else {
        // 新增
        await createArticle(payload)
        ElMessage.success('新增成功')
      }
      visible.value = false
      // 通知父页面刷新文章列表
      emit('refresh')
    } catch (e) {
      ElMessage.error('操作失败')
      console.error('文章操作失败', e)
    }
  })
}
function resetForm() {
  form.value.scope = ''
  form.value.name = ''
  form.value.img = null
  form.value.coverUrl = ''
  form.value.content = ''
  form.value.group = ''
  form.value.id = undefined
  form.value.visibleOrgId = undefined // 重置可见范围ID
  form.value.visibleOrgName = undefined // 重置可见范围名称
}
// 图片上传
async function handleImageUploadRequest(option: any) {
  const formData = new FormData()
  formData.append('file', option.file)
  try {
    const res = await request.post({
      url: '/infra/file/upload',
      data: formData,
      headers: { 'Content-Type': 'multipart/form-data' }
    })
    let coverUrl = ''
    if (typeof res === 'string') {
      coverUrl = res
    } else if (res && res.data && typeof res.data === 'string') {
      coverUrl = res.data
    } else if (res && res.data && res.data.data) {
      coverUrl = res.data.data
    }
    if (coverUrl) {
      form.value.coverUrl = coverUrl
      imgFileList.value = [
        {
          name: option.file.name,
          url: coverUrl,
          status: 'done',
          uid: option.file.uid || Date.now().toString()
        }
      ]
      option.onSuccess(res, option.file)
    } else {
      option.onError(new Error(res.data?.msg || '上传失败'))
    }
  } catch (e) {
    option.onError(e)
  }
}
function handleRemoveImage() {
  form.value.coverUrl = ''
  imgFileList.value = []
}
// 新增：用于父页面监听弹窗关闭后的刷新
const emit = defineEmits(['refresh'])

// 部门树相关
const deptList = ref<any[]>([])
const deptTreeRef = ref()
const deptSearchKeyword = ref('')

// 获取部门树数据
async function loadDeptList() {
  const res = await getSimpleDeptList()
  // 只取parentId=0的机构及其子机构
  const topLevelDepts = res.filter((dept: any) => dept.parentId === 0)
  const filteredDepts = topLevelDepts.map((topDept: any) => {
    const children = res.filter((dept: any) => dept.parentId === topDept.id)
    return {
      ...topDept,
      children: children.length > 0 ? children : undefined
    }
  })
  deptList.value = filteredDepts
}

const filterDeptNode = (keyword: string, data: any) => {
  if (!keyword) return true
  return data.name.includes(keyword)
}

const handleDeptNodeClick = (row: any) => {
  if (row.parentId === 0) {
    ElMessage.warning('请选择具体机构')
    return
  }
  form.value.visibleOrgId = row.id
  form.value.visibleOrgName = row.name
}

const clearDept = () => {
  form.value.visibleOrgId = undefined
  form.value.visibleOrgName = undefined
}

watch(deptSearchKeyword, (val) => {
  nextTick(() => {
    deptTreeRef.value?.filter(val)
  })
})

// 弹窗打开时加载部门树
watch(visible, (val) => {
  if (val) loadDeptList()
})

const groupOptions = ref<any[]>([])

watch(() => form.value.visibleOrgId, async (newVal) => {
  form.value.group = ''
  groupOptions.value = []
  if (newVal) {
    await loadGroupOptions(newVal)
  }
})

async function loadGroupOptions(visibleOrgId: any) {
  try {
    const res = await getCategoryList()
    let list: any[] = []
    if (Array.isArray(res)) {
      list = res
    } else if (res && Array.isArray(res.data)) {
      list = res.data
    } else if (res && res.data && Array.isArray(res.data.list)) {
      list = res.data.list
    }
    groupOptions.value = list.filter(item => item.visibleOrgId === visibleOrgId)
  } catch (e) {
    groupOptions.value = []
  }
}
function handleGroupFocus() {
  if (!form.value.visibleOrgId) {
    ElMessage.warning('请先选择可见范围')
  }
}

defineExpose({ open })
</script>
