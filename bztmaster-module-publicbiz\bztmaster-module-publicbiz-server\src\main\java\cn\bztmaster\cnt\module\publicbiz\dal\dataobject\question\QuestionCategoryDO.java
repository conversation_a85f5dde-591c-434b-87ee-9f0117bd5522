package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question;

import cn.bztmaster.cnt.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * 考题分类表 DO
 *
 * <AUTHOR>
 */
@TableName("publicbiz_question_category")
@KeySequence("publicbiz_question_category_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "考题分类表 DO")
public class QuestionCategoryDO extends TenantBaseDO {

    /**
     * 主键，自增
     */
    @TableId
    private Long id;

    // ========== 分类层级信息 ==========
    /**
     * 一级分类名称
     */
    private String level1Name;
    /**
     * 一级分类代码
     */
    private String level1Code;
    /**
     * 二级分类名称
     */
    private String level2Name;
    /**
     * 二级分类代码
     */
    private String level2Code;
    /**
     * 三级分类名称
     */
    private String level3Name;
    /**
     * 三级分类代码
     */
    private String level3Code;

    // ========== 认定点信息 ==========
    /**
     * 认定点名称
     */
    private String certName;
    /**
     * 认定点代码
     */
    private String certCode;

    // ========== 业务分类 ==========
    /**
     * 业务模块：家政业务、高校业务、培训业务、认证业务
     */
    private String biz;
    /**
     * 业务模块名称
     */
    private String bizName;

    // ========== 分类属性 ==========
    /**
     * 父级分类ID，0表示顶级分类
     */
    private Long parentId;
    /**
     * 分类层级：1-一级，2-二级，3-三级
     */
    private Integer level;
    /**
     * 排序序号
     */
    private Integer sortOrder;
    /**
     * 分类描述
     */
    private String description;
    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 创建人姓名
     */
    private String creatorName;
}
