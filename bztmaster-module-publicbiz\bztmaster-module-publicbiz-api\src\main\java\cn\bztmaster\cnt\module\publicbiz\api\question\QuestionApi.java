package cn.bztmaster.cnt.module.publicbiz.api.question;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.publicbiz.api.question.dto.QuestionRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * 考题管理 API 接口
 *
 * <AUTHOR>
 */
@FeignClient(name = "publicbiz-service")
@Tag(name = "RPC 服务 - 考题管理")
public interface QuestionApi {

    String PREFIX = "/publicbiz/question";

    @GetMapping(PREFIX + "/get")
    @Operation(summary = "通过考题ID查询考题")
    @Parameter(name = "id", description = "考题编号", example = "1", required = true)
    CommonResult<QuestionRespDTO> getQuestion(@RequestParam("id") Long id);

    @GetMapping(PREFIX + "/list")
    @Operation(summary = "通过考题ID查询考题们")
    @Parameter(name = "ids", description = "考题编号数组", example = "1,2", required = true)
    CommonResult<List<QuestionRespDTO>> getQuestionList(@RequestParam("ids") Collection<Long> ids);

    // 可根据业务需求继续补充接口
}
