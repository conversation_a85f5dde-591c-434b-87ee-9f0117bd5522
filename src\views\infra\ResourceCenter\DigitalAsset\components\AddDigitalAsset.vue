<template>
  <el-drawer
    v-model="visible"
    title="新增课程"
    size="600px"
    direction="rtl"
    :with-header="true"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <el-form :model="form" :rules="rules" ref="formRef" label-width="100px" status-icon>
      <el-form-item label="课程名称" prop="name" required>
        <el-input v-model="form.name" placeholder="必填" />
      </el-form-item>
      <el-form-item label="授课方式" prop="teachType">
        <el-select v-model="form.teachType" placeholder="请选择">
          <el-option
            v-for="item in teachTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="课程封面" prop="cover">
        <el-upload
          class="upload-demo"
          action="#"
          :auto-upload="false"
          :show-file-list="true"
          :limit="1"
          :on-change="handleCoverChange"
          :file-list="form.coverUrl ? [{ url: form.coverUrl, name: '课程封面' }] : []"
          list-type="picture-card"
        >
          <el-icon><Plus /></el-icon>
          <template #tip>
            <div style="color: #888; margin-left: 10px; display: inline-block"
              >仅支持上传1张图片，上传后自动填充URL</div
            >
          </template>
        </el-upload>
        <div v-if="form.coverUrl" style="margin-top: 8px">
          <el-image
            :src="form.coverUrl"
            style="width: 80px; height: 80px; object-fit: contain; border: 1px solid #eee"
            :preview-src-list="[form.coverUrl]"
          />
        </div>
      </el-form-item>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="课程分类" prop="category">
            <el-select v-model="form.category" placeholder="请选择">
              <el-option
                v-for="item in categoryOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="课程状态" prop="status">
            <el-select v-model="form.status" placeholder="请选择">
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="关联讲师" prop="teacherName">
            <el-select
              v-model="form.teacherId"
              placeholder="请选择讲师"
              @change="handleTeacherChange"
            >
              <el-option
                v-for="item in teacherOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 线下授课专用字段 -->
      <template v-if="form.teachType === '线下授课'">
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="上课地址" prop="location">
              <el-input v-model="form.location" placeholder="请输入上课地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排期" prop="schedule">
              <el-input v-model="form.schedule" placeholder="如：每周一、三、五 9:00-17:00" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="总名额" prop="totalSeats">
              <el-input v-model="form.totalSeats" placeholder="请输入总名额" type="number" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="已报名人数" prop="enrolledCount">
              <el-input v-model="form.enrolledCount" placeholder="请输入已报名人数" type="number" />
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <!-- 线上授课专用字段 -->
      <template v-if="form.teachType === '线上授课'">
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="课程总时长" prop="totalDuration">
              <el-input
                v-model="form.totalDuration"
                placeholder="请输入课程总时长（小时）"
                type="number"
                step="0.1"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="业务模块" prop="businessModule">
            <el-select v-model="form.businessModule" placeholder="请选择业务模块">
              <el-option
                v-for="item in businessModuleOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="收款商户" prop="merchant">
            <el-select
              v-model="form.merchant"
              placeholder="请选择收款商户"
              @change="handleMerchantChange"
            >
              <el-option
                v-for="item in merchantOptions"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="课程描述" prop="description">
        <el-input v-model="form.description" type="textarea" :rows="3" placeholder="" />
      </el-form-item>
      <div style="text-align: right">
        <el-button @click="onCancel" :disabled="loading">取消</el-button>
        <el-button type="primary" @click="onSubmit" :loading="loading">保存</el-button>
      </div>
    </el-form>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, defineExpose, defineEmits, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { DigitalAssetApi, type AddCourseParams } from '@/api/infra/digitalAsset'
import { getDictDataPage } from '@/api/system/dict/dict.data'
import { getAppList } from '@/api/pay/app'
import request from '@/config/axios'

const emit = defineEmits(['close', 'success'])
const visible = defineModel<boolean>('visible', { default: false })
const formRef = ref()
const loading = ref(false)

const form = reactive({
  name: '',
  teachType: '',
  coverUrl: '',
  category: '',
  status: '',
  teacherId: undefined as number | undefined,
  teacherName: '',
  businessModule: '',
  merchant: undefined as number | undefined,
  merchantName: '',
  description: '',
  // 线下授课专用字段
  location: '',
  schedule: '',
  totalSeats: undefined as number | undefined,
  enrolledCount: 0,
  // 线上授课专用字段
  totalDuration: undefined as number | undefined
})
const rules = {
  name: [{ required: true, message: '请输入课程名称', trigger: 'blur' }],
  teachType: [{ required: true, message: '请选择授课方式', trigger: 'change' }],
  category: [{ required: true, message: '请选择课程分类', trigger: 'change' }],
  status: [{ required: true, message: '请选择课程状态', trigger: 'change' }]
}
const teachTypeOptions = [
  { label: '线上授课', value: '线上授课' },
  { label: '线下授课', value: '线下授课' }
]
const categoryOptions = [
  { label: '家政技能', value: '家政技能' },
  { label: '职业素养', value: '职业素养' },
  { label: '高校实践', value: '高校实践' },
  { label: '企业管理', value: '企业管理' }
]
const statusOptions = [
  { label: '待发布', value: '待发布' },
  { label: '已上架', value: '已上架' },
  { label: '已下架', value: '已下架' }
]
const businessModuleOptions = ref<any[]>([])
const merchantOptions = ref<any[]>([])

const teacherOptions = ref<any[]>([])

function handleCoverChange(file: any) {
  if (file && file.raw) {
    const formData = new FormData()
    formData.append('file', file.raw)
    formData.append('directory', 'course')
    request
      .postOriginal({
        url: '/infra/file/upload',
        data: formData,
        headers: { 'Content-Type': 'multipart/form-data' }
      })
      .then((res) => {
        if (res.code === 0 && res.data && /^https?:\/\//.test(res.data)) {
          form.coverUrl = res.data
          ElMessage.success('上传成功')
        } else {
          form.coverUrl = ''
          ElMessage.error(res.msg || '上传失败')
        }
      })
      .catch(() => {
        form.coverUrl = ''
        ElMessage.error('上传失败')
      })
  }
}

function handleTeacherChange(teacherId: number) {
  // 根据选择的讲师ID设置讲师信息
  const teacher = teacherOptions.value.find((item) => item.id === teacherId)
  if (teacher) {
    form.teacherId = teacher.id
    form.teacherName = teacher.name
  }
}

function handleMerchantChange(merchantId: number) {
  // 根据选择的商户ID设置商户名称
  const merchant = merchantOptions.value.find((item) => item.id === merchantId)
  if (merchant) {
    form.merchant = merchant.id
    form.merchantName = merchant.name
  }
}

function onCancel() {
  visible.value = false
  emit('close')
}

async function onSubmit() {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    loading.value = true

    // 构建请求参数，严格按照接口文档字段
    const params: AddCourseParams = {
      name: form.name,
      teachType: form.teachType,
      category: form.category,
      status: form.status,
      description: form.description || undefined,
      coverUrl: form.coverUrl || undefined,
      teacherId: form.teacherId || undefined,
      teacherName: form.teacherName || undefined,
      businessModule: form.businessModule || undefined,
      merchant: form.merchant || undefined,
      merchantName: form.merchantName || undefined,
      enrolledCount: form.enrolledCount || 0
    }

    // 根据授课方式添加专用字段
    if (form.teachType === '线下授课') {
      params.location = form.location || undefined
      params.schedule = form.schedule || undefined
      params.totalSeats = form.totalSeats || undefined
    } else if (form.teachType === '线上授课') {
      params.totalDuration = form.totalDuration || undefined
    }

    // 调用API保存课程
    const result = await DigitalAssetApi.addCourse(params)

    ElMessage.success('课程创建成功')
    visible.value = false
    emit('close')
    emit('success', result)

    // 重置表单
    resetForm()
  } catch (error) {
    console.error('保存课程失败:', error)
    ElMessage.error('保存失败，请重试')
  } finally {
    loading.value = false
  }
}

function resetForm() {
  Object.assign(form, {
    name: '',
    teachType: '',
    coverUrl: '',
    category: '',
    status: '',
    teacherId: undefined,
    teacherName: '',
    businessModule: '',
    merchant: undefined,
    merchantName: '',
    description: '',
    location: '',
    schedule: '',
    totalSeats: undefined,
    enrolledCount: 0,
    totalDuration: undefined
  })
}
function open() {
  visible.value = true
}

/** 获取业务模块字典数据 */
const fetchBusinessModuleOptions = async () => {
  try {
    const res = await getDictDataPage({ dictType: 'business_module', pageNo: 1, pageSize: 100 })
    businessModuleOptions.value = (res?.list || []).map((item) => ({
      label: item.label,
      value: item.value
    }))
  } catch (error) {
    console.error('获取业务模块字典数据失败:', error)
  }
}

/** 获取讲师列表数据 */
const fetchTeacherOptions = async () => {
  try {
    const res = await request.get({ url: '/publicbiz/teacher/list' })
    console.log('讲师接口返回数据:', res)

    // 根据接口文档，数据直接在res中，不是res.data
    if (res && Array.isArray(res)) {
      teacherOptions.value = res.map((item: any) => ({
        id: item.id,
        name: item.name,
        label: item.name,
        value: item.id
      }))
      console.log('处理后的讲师选项:', teacherOptions.value)
    } else if (res.data && Array.isArray(res.data)) {
      // 如果数据在res.data中
      teacherOptions.value = res.data.map((item: any) => ({
        id: item.id,
        name: item.name,
        label: item.name,
        value: item.id
      }))
      console.log('处理后的讲师选项:', teacherOptions.value)
    } else {
      console.log('接口返回数据格式不符合预期:', res)
    }
  } catch (error) {
    console.error('获取讲师列表失败:', error)
  }
}

/** 获取商户列表数据 */
const fetchMerchantOptions = async () => {
  try {
    const res = await getAppList()
    console.log('支付应用接口返回数据:', res)

    if (Array.isArray(res)) {
      console.log('数据直接在res中，数组长度:', res.length)
      // 直接使用应用数据作为商户选项
      merchantOptions.value = res.map((app: any) => ({
        id: app.id,
        name: app.name,
        label: app.name,
        value: app.id
      }))
      console.log('处理后的商户选项:', merchantOptions.value)
    } else {
      console.log('支付应用接口返回数据格式不符合预期:', res)
    }
  } catch (error) {
    console.error('获取商户列表失败:', error)
  }
}

onMounted(() => {
  fetchBusinessModuleOptions()
  fetchTeacherOptions()
  fetchMerchantOptions()
})

defineExpose({ open })
</script>

<style scoped>
.upload-demo {
  display: block;
}
</style>
