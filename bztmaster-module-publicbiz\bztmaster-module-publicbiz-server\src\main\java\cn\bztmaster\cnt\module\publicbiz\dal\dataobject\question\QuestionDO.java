package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question;

import cn.bztmaster.cnt.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;

/**
 * 考题主表 DO
 *
 * <AUTHOR>
 */
@TableName("publicbiz_question")
@KeySequence("publicbiz_question_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "考题主表 DO")
public class QuestionDO extends TenantBaseDO {

    /**
     * 主键，自增
     */
    @TableId
    private Long id;

    // ========== 分类信息字段 ==========
    /**
     * 一级分类名称，如：职业技能等级认定
     */
    private String level1Name;
    /**
     * 一级分类代码，如：ZY001
     */
    private String level1Code;
    /**
     * 二级分类名称，如：家政服务类
     */
    private String level2Name;
    /**
     * 二级分类代码，如：JZ001
     */
    private String level2Code;
    /**
     * 三级分类名称，如：家政服务员
     */
    private String level3Name;
    /**
     * 三级分类代码，如：JZFW001
     */
    private String level3Code;

    // ========== 认定点信息 ==========
    /**
     * 认定点名称，如：职业道德基础
     */
    private String certName;
    /**
     * 认定点代码，如：KP001
     */
    private String certCode;

    // ========== 题目内容 ==========
    /**
     * 题干内容
     */
    private String title;
    /**
     * 题型：单选题、多选题、判断题、简答题、填空题、材料题、排序题、匹配题、文件上传题
     */
    private String type;
    /**
     * 参考答案
     */
    private String answer;

    // ========== 业务分类 ==========
    /**
     * 业务模块：家政业务、高校业务、培训业务、认证业务
     */
    private String biz;
    /**
     * 业务模块名称
     */
    private String bizName;

    // ========== 扩展字段 ==========
    /**
     * 难度等级：1-简单，2-中等，3-困难
     */
    private Integer difficulty;
    /**
     * 题目分值
     */
    private BigDecimal score;
    /**
     * 答题时间限制（秒），0表示无限制
     */
    private Integer timeLimit;
    /**
     * 题目解析
     */
    private String explanation;
    /**
     * 关键词，用逗号分隔
     */
    private String keywords;
    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 创建人姓名
     */
    private String creatorName;
}
