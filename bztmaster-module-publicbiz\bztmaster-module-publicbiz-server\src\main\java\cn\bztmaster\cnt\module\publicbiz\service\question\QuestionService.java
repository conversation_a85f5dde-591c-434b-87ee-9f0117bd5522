package cn.bztmaster.cnt.module.publicbiz.service.question;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.QuestionPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.QuestionRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.QuestionSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionDO;

import java.util.Collection;
import java.util.List;

/**
 * 考题管理 Service 接口
 *
 * <AUTHOR>
 */
public interface QuestionService {

    /**
     * 创建考题
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createQuestion(QuestionSaveReqVO createReqVO);

    /**
     * 更新考题
     *
     * @param updateReqVO 更新信息
     */
    void updateQuestion(QuestionSaveReqVO updateReqVO);

    /**
     * 删除考题
     *
     * @param id 编号
     */
    void deleteQuestion(Long id);

    /**
     * 获得考题
     *
     * @param id 编号
     * @return 考题
     */
    QuestionRespVO getQuestion(Long id);

    /**
     * 获得考题分页
     *
     * @param pageReqVO 分页查询
     * @return 考题分页
     */
    PageResult<QuestionRespVO> getQuestionPage(QuestionPageReqVO pageReqVO);

    // ==================== API 接口需要的方法 ====================

    /**
     * 获得考题 DO
     *
     * @param id 编号
     * @return 考题 DO
     */
    QuestionDO getQuestionDO(Long id);

    /**
     * 获得考题 DO 列表
     *
     * @param ids 编号
     * @return 考题 DO 列表
     */
    List<QuestionDO> getQuestionDOList(Collection<Long> ids);
}
