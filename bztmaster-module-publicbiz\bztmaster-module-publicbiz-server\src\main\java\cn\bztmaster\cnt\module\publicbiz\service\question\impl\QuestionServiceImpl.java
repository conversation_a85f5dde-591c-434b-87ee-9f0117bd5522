package cn.bztmaster.cnt.module.publicbiz.service.question.impl;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.QuestionPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.QuestionRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.QuestionSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.convert.question.QuestionConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.question.QuestionMapper;
import cn.bztmaster.cnt.module.publicbiz.service.question.QuestionService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

import static cn.bztmaster.cnt.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.bztmaster.cnt.module.publicbiz.enums.ErrorCodeConstants.QUESTION_NOT_EXISTS;

/**
 * 考题管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class QuestionServiceImpl implements QuestionService {

    @Resource
    private QuestionMapper questionMapper;

    @Resource
    private QuestionConvert questionConvert;

    @Override
    public Long createQuestion(QuestionSaveReqVO createReqVO) {
        // 插入
        QuestionDO question = questionConvert.convert(createReqVO);
        question.setCreateTime(LocalDateTime.now());
        question.setDeleted(false);
        questionMapper.insert(question);
        // 返回
        return question.getId();
    }

    @Override
    public void updateQuestion(QuestionSaveReqVO updateReqVO) {
        // 校验存在
        validateQuestionExists(updateReqVO.getId());
        // 更新
        QuestionDO updateObj = questionConvert.convert(updateReqVO);
        updateObj.setUpdateTime(LocalDateTime.now());
        questionMapper.updateById(updateObj);
    }

    @Override
    public void deleteQuestion(Long id) {
        // 校验存在
        validateQuestionExists(id);
        // 删除
        questionMapper.deleteById(id);
    }

    private void validateQuestionExists(Long id) {
        if (questionMapper.selectById(id) == null) {
            throw exception(QUESTION_NOT_EXISTS);
        }
    }

    @Override
    public QuestionRespVO getQuestion(Long id) {
        QuestionDO question = questionMapper.selectById(id);
        return questionConvert.convert(question);
    }

    @Override
    public PageResult<QuestionRespVO> getQuestionPage(QuestionPageReqVO pageReqVO) {
        PageResult<QuestionDO> pageResult = questionMapper.selectPage(pageReqVO);
        return questionConvert.convertPage(pageResult);
    }

    // ==================== API 接口需要的方法 ====================

    @Override
    public QuestionDO getQuestionDO(Long id) {
        return questionMapper.selectById(id);
    }

    @Override
    public List<QuestionDO> getQuestionDOList(Collection<Long> ids) {
        return questionMapper.selectBatchIds(ids);
    }
}
