<template>
  <el-dialog
    v-model="visible"
    title="分配线索"
    width="400px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <div style="margin-bottom: 16px">
      将线索 <b>"{{ clueInfo.customerName }}"</b> (ID: {{ clueInfo.id }}) 分配给：
    </div>
    <el-form label-width="0" v-loading="loading">
      <el-form-item>
        <el-tree-select
          v-model="selectedDeptId"
          :data="deptTree"
          :props="defaultProps"
          check-strictly
          default-expand-all
          placeholder="请选择部门"
          style="width: 100%"
          @change="onDeptChange"
        />
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="selectedFollower"
          placeholder="请选择跟进人"
          style="width: 100%"
          :disabled="!selectedDeptId"
          @change="onFollowerChange"
        >
          <el-option
            v-for="item in followerOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div v-if="currentSelection" style="margin: 12px 0; color: #888; font-size: 13px">
      当前选择：{{ currentSelection }}
    </div>
    <template #footer>
      <el-button @click="onCancel">取消</el-button>
      <el-button type="primary" @click="onConfirm" :disabled="!selectedDeptId || !selectedFollower"
        >确认分配</el-button
      >
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineExpose } from 'vue'
import { ElMessage } from 'element-plus'

// 定义事件
const emit = defineEmits(['success'])
import { defaultProps, handleTree } from '@/utils/tree'
import * as DeptApi from '@/api/system/dept'
import * as UserApi from '@/api/system/user'
import { ClueCenterApi } from '@/api/infra/clueCenter'

const visible = ref(false)
const loading = ref(false)
const clueInfo = reactive({
  id: '',
  customerName: ''
})

// 部门树数据和选择状态
const deptTree = ref<any[]>([])
const selectedDeptId = ref('')
const selectedDeptName = ref('')
const followerOptions = ref<
  { label: string; value: number; userId: number; nickname: string; username: string }[]
>([])
const selectedFollower = ref<number | ''>('')
const selectedFollowerInfo = ref<{ nickname: string; username: string } | null>(null)

const currentSelection = computed(() => {
  if (!selectedDeptName.value || !selectedFollower.value || !selectedFollowerInfo.value) return ''
  return `${selectedDeptName.value} - ${selectedFollowerInfo.value.nickname} (${selectedFollowerInfo.value.username})`
})

// 获取部门树数据
async function getDeptTree() {
  try {
    loading.value = true
    const data = await DeptApi.getSimpleDeptList()
    deptTree.value = handleTree(data)
  } catch (error) {
    console.error('获取部门树失败:', error)
    ElMessage.error('获取部门数据失败')
  } finally {
    loading.value = false
  }
}

// 部门选择变化
async function onDeptChange(value: any, node: any) {
  selectedDeptId.value = value
  selectedDeptName.value = node?.name || ''
  selectedFollower.value = ''
  selectedFollowerInfo.value = null

  // 根据选择的部门获取跟进人列表
  await loadFollowersByDept(value)
}

// 跟进人选择变化
function onFollowerChange(value: number) {
  const selectedOption = followerOptions.value.find((option) => option.value === value)
  if (selectedOption) {
    selectedFollowerInfo.value = {
      nickname: selectedOption.nickname,
      username: selectedOption.username
    }
  } else {
    selectedFollowerInfo.value = null
  }
}

// 根据部门加载跟进人
async function loadFollowersByDept(deptId: string) {
  if (!deptId) {
    followerOptions.value = []
    return
  }

  try {
    // 调用用户API，根据部门ID查询该部门的用户列表
    const queryParams: any = {
      pageNo: 1,
      pageSize: 100, // 获取足够多的用户
      deptId: deptId,
      status: 0 // 只获取正常状态的用户
    }
    const data = await UserApi.getUserPage(queryParams)

    // 将用户数据转换为选项格式
    followerOptions.value = data.list.map((user: any) => ({
      label: `${user.nickname} (${user.username})`,
      value: user.id, // 使用用户ID作为value
      userId: user.id,
      nickname: user.nickname,
      username: user.username
    }))
  } catch (error) {
    console.error('获取部门用户失败:', error)
    ElMessage.error('获取跟进人列表失败')
    followerOptions.value = []
  }
}

async function open(clue: any) {
  visible.value = true
  clueInfo.id = clue.id || ''
  clueInfo.customerName = clue.customerName || ''
  selectedDeptId.value = ''
  selectedDeptName.value = ''
  selectedFollower.value = ''
  selectedFollowerInfo.value = null
  followerOptions.value = []

  // 加载部门树数据
  await getDeptTree()
}

function close() {
  visible.value = false
}

function onCancel() {
  close()
}

async function onConfirm() {
  if (!selectedDeptId.value || !selectedFollower.value) {
    ElMessage.warning('请选择部门和跟进人')
    return
  }

  try {
    // 调用分配线索API
    await ClueCenterApi.assignClue({
      id: clueInfo.id,
      dept1: selectedDeptName.value,
      dept2: selectedDeptName.value, // 简化处理，实际可能需要解析部门层级
      dept3: selectedDeptName.value,
      userId: selectedFollower.value, // 传递用户ID
      userName: selectedFollowerInfo.value?.nickname || '' // 传递用户姓名
    })

    ElMessage.success('分配成功')
    emit('success') // 通知父组件刷新列表
    close()
  } catch (error) {
    console.error('分配线索失败:', error)
    ElMessage.error('分配失败，请重试')
  }
}

defineExpose({ open, close })
</script>

<style scoped lang="scss">
.el-dialog__body {
  padding-bottom: 0;
}
</style>
