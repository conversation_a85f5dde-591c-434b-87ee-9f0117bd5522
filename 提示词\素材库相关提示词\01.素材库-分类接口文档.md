## 新增分类

**接口地址**:`/system/material/category/create`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
{
  "name": "",
  "parentId": 0,
  "sort": 0,
  "status": 0,
  "description": "",
  "visibleOrgId": 0,
  "visibleOrgName": ""
}
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| categoryCreateReqVO | 素材分类 - 新增请求 VO | body | true | CategoryCreateReqVO | CategoryCreateReqVO |
| &emsp;&emsp;name | 分类名称 |  | true | string |  |
| &emsp;&emsp;parentId | 父分类ID，默认0 |  | false | integer(int64) |  |
| &emsp;&emsp;sort | 排序，数字越小越靠前，默认0 |  | false | integer(int32) |  |
| &emsp;&emsp;status | 状态，0-禁用，1-启用，默认1 |  | false | integer(int32) |  |
| &emsp;&emsp;description | 分类描述 |  | false | string |  |
| &emsp;&emsp;visibleOrgId | 可视范围机构ID |  | false | integer(int64) |  |
| &emsp;&emsp;visibleOrgName | 可视范围机构名称 |  | false | string |  |

**响应状态**:

| 状态码 | 说明 | schema           |
| ------ | ---- | ---------------- |
| 200    | OK   | CommonResultLong |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | integer(int64) | integer(int64) |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": 0,
	"msg": ""
}
```

## 删除分类

**接口地址**:`/system/material/category/delete`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
{
  "id": 0
}
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| categoryDeleteReqVO | 素材分类 - 删除请求 VO | body | true | CategoryDeleteReqVO | CategoryDeleteReqVO |
| &emsp;&emsp;id | 分类ID |  | true | integer(int64) |  |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 分类列表

**接口地址**:`/system/material/category/list`

**请求方式**:`GET`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

暂无

**响应状态**:

| 状态码 | 说明 | schema                             |
| ------ | ---- | ---------------------------------- |
| 200    | OK   | CommonResultListCategoryListRespVO |

**响应参数**:

| 参数名称                   | 参数说明             | 类型           | schema             |
| -------------------------- | -------------------- | -------------- | ------------------ |
| code                       |                      | integer(int32) | integer(int32)     |
| data                       |                      | array          | CategoryListRespVO |
| &emsp;&emsp;id             | 分类ID               | integer(int64) |                    |
| &emsp;&emsp;name           | 分类名称             | string         |                    |
| &emsp;&emsp;parentId       | 父分类ID             | integer(int64) |                    |
| &emsp;&emsp;sort           | 排序，数字越小越靠前 | integer(int32) |                    |
| &emsp;&emsp;status         | 状态，0-禁用，1-启用 | integer(int32) |                    |
| &emsp;&emsp;description    | 分类描述             | string         |                    |
| &emsp;&emsp;level          | 分类层级             | integer(int32) |                    |
| &emsp;&emsp;path           | 分类路径             | string         |                    |
| &emsp;&emsp;visibleOrgId   | 可视范围机构ID       | integer(int64) |                    |
| &emsp;&emsp;visibleOrgName | 可视范围机构名称     | string         |                    |
| msg                        |                      | string         |                    |

**响应示例**:

```javascript
{
	"code": 0,
	"data": [
		{
			"id": 1,
			"name": "图片素材",
			"parentId": 0,
			"sort": 0,
			"status": 1,
			"description": "",
			"level": 1,
			"path": "1,2,3",
			"visibleOrgId": 0,
			"visibleOrgName": ""
		}
	],
	"msg": ""
}
```

## 编辑分类

**接口地址**:`/system/material/category/update`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
{
  "id": 0,
  "name": "",
  "parentId": 0,
  "sort": 0,
  "status": 0,
  "description": "",
  "visibleOrgId": 0,
  "visibleOrgName": ""
}
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| categoryUpdateReqVO | 素材分类 - 编辑请求 VO | body | true | CategoryUpdateReqVO | CategoryUpdateReqVO |
| &emsp;&emsp;id | 分类ID |  | true | integer(int64) |  |
| &emsp;&emsp;name | 分类名称 |  | true | string |  |
| &emsp;&emsp;parentId | 父分类ID，默认0 |  | false | integer(int64) |  |
| &emsp;&emsp;sort | 排序，数字越小越靠前，默认0 |  | false | integer(int32) |  |
| &emsp;&emsp;status | 状态，0-禁用，1-启用，默认1 |  | false | integer(int32) |  |
| &emsp;&emsp;description | 分类描述 |  | false | string |  |
| &emsp;&emsp;visibleOrgId | 可视范围机构ID |  | false | integer(int64) |  |
| &emsp;&emsp;visibleOrgName | 可视范围机构名称 |  | false | string |  |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```
