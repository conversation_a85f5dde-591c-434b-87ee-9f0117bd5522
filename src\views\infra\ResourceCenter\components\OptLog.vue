<!--
  页面名称：操作日志
  功能描述：展示合作伙伴的操作日志，右侧抽屉，时间线样式，支持关闭
-->
<template>
  <el-drawer
    v-model="visible"
    :title="`${partnerName}的操作日志`"
    size="600px"
    :with-header="true"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <div class="optlog-timeline-wrap">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>
      <div v-else-if="error" class="error-container">
        <el-empty description="加载失败，请重试" />
        <el-button type="primary" @click="loadLogs">重新加载</el-button>
      </div>
      <div v-else>
        <el-timeline v-if="logs.length > 0">
          <el-timeline-item
            v-for="(item, idx) in logs"
            :key="idx"
            :timestamp="formatTime(item.createTime)"
            placement="top"
          >
            <div class="optlog-item">
              <div class="optlog-meta">
                <span class="optlog-user">{{ item.userName }}</span>
                <el-tag size="small" type="info">{{ item.subType }}</el-tag>
              </div>
              <div class="optlog-content">{{ item.action }}</div>
            </div>
          </el-timeline-item>
        </el-timeline>
        <el-empty v-else description="暂无操作日志" />
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { getPartnerOperateLog } from '@/api/infra/business/partner'

const visible = ref(false)
const partnerName = ref('')
const logs = ref<any[]>([])
const loading = ref(false)
const error = ref(false)
const currentBizId = ref<number>()

// 格式化时间
const formatTime = (time: any) => {
  if (!time) return ''
  const date = new Date(time)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 加载操作日志
const loadLogs = async () => {
  if (!currentBizId.value) return
  
  loading.value = true
  error.value = false
  
  try {
    const res = await getPartnerOperateLog({
      pageNo: 1,
      pageSize: 50,
      bizId: currentBizId.value,
      type: 'PARTNER 合作伙伴'
    })
    
    if (res.list) {
      logs.value = res.list
    } else {
      logs.value = []
    }
  } catch (e) {
    console.error('加载操作日志失败:', e)
    error.value = true
    ElMessage.error('加载操作日志失败')
  } finally {
    loading.value = false
  }
}

// 对外暴露打开方法，支持传入机构名和业务ID
const open = (name: string, bizId: number) => {
  partnerName.value = name
  currentBizId.value = bizId
  visible.value = true
  loadLogs()
}

defineExpose({ open })
</script>

<style scoped lang="scss">
.optlog-timeline-wrap {
  padding: 24px 0 0 0;
}
.loading-container {
  padding: 20px;
}
.error-container {
  padding: 40px;
  text-align: center;
}
.optlog-item {
  background: #f7fafd;
  border-radius: 8px;
  padding: 12px 18px;
  margin-bottom: 8px;
}
.optlog-meta {
  color: #888;
  font-size: 13px;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}
.optlog-user {
  font-weight: bold;
  margin-right: 8px;
}
.optlog-content {
  font-size: 15px;
  color: #222;
  word-break: break-all;
}
</style>
