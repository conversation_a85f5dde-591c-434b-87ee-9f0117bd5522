<!--
  页面名称：素材回收站
  功能描述：展示被删除的素材信息，支持搜索、分页、批量恢复、批量永久删除、单项操作等
-->
<template>
  <div>
    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-input
        v-model="searchForm.name"
        placeholder="素材名称"
        clearable
        style="width: 200px; margin-right: 16px"
      />
      <el-select
        v-model="searchForm.source"
        placeholder="请选择来源"
        style="width: 160px; margin-right: 16px"
        clearable
      >
        <el-option label="请选择" value="" />
        <el-option
          v-for="item in sourceOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-model="searchForm.type"
        placeholder="请选择素材类型"
        style="width: 160px; margin-right: 16px"
      >
        <el-option label="图片" value="image" />
        <el-option label="视频" value="video" />
        <el-option label="文章" value="article" />
        <el-option label="文档" value="document" />
        <el-option label="图文" value="imageText" />
      </el-select>
      <el-button type="primary" @click="onSearch">查询</el-button>
      <el-button @click="onReset">重置</el-button>
    </div>
    <!-- 批量操作按钮 -->
    <div class="mb-8px batch-actions">
      <el-checkbox v-model="isAllSelected" @change="onSelectAll">全选</el-checkbox>
      <el-button :disabled="!selectedIds.length" @click="onBatchRestore">恢复选中</el-button>
      <el-button type="danger" :disabled="!selectedIds.length" @click="onBatchDelete"
        >永久删除</el-button
      >
    </div>
    <!-- 回收站提示 -->
    <el-alert type="warning" show-icon class="mb-16px">
      <template #title>
        <b>回收站提醒：</b>
        素材在回收站中保留30天，超过30天将自动永久删除。请及时处理需要恢复的素材。
      </template>
    </el-alert>
    <!-- 表格区 -->
    <el-table :data="tableData" @selection-change="onSelectionChange" style="width: 100%">
      <el-table-column type="selection" width="50" />
      <el-table-column label="素材内容" width="120">
        <template #default="{ row }">
          <template v-if="searchForm.type === 'image'">
            <img
              v-if="row.url"
              :src="row.url"
              alt="图片"
              style="width: 60px; height: 60px; object-fit: cover"
            />
            <span v-else>无</span>
          </template>
          <template v-else-if="searchForm.type === 'video'">
            <video
              v-if="row.url"
              :src="row.url"
              controls
              style="width: 80px; height: 60px; object-fit: cover"
            ></video>
            <span v-else>无</span>
          </template>
          <template v-else-if="searchForm.type === 'article'">
            <span>{{ row.title || row.name || '文章' }}</span>
          </template>
          <template v-else-if="searchForm.type === 'document'">
            <span>{{ row.documentName || '文档' }}</span>
          </template>
          <template v-else-if="searchForm.type === 'imageText'">
            <img
              v-if="row.thumbUrl || row.url"
              :src="row.thumbUrl || row.url"
              alt="图文"
              style="width: 60px; height: 60px; object-fit: cover"
            />
            <span v-else>图文</span>
          </template>
          <template v-else>--</template>
        </template>
      </el-table-column>
      <el-table-column prop="id" label="素材ID" width="120" />
      <el-table-column prop="name" label="素材名称" />
      <el-table-column label="素材来源">
        <template #default="{ row }">
          <span>{{ row.visibleOrgName || row.sourceOrgName || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="删除时间">
        <template #default="{ row }">
          <span>{{ formatDate(row.updateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="剩余天数">
        <template #default="{ row }">
          <span>{{ calcRemainDays(row.updateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="140">
        <template #default="{ row }">
          <el-button type="text" @click="onRestore(row)">恢复</el-button>
          <el-button type="text" style="color: #f56c6c" @click="onDelete(row)">永久删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页区 -->
    <el-pagination
      v-model:current-page="pagination.page"
      v-model:page-size="pagination.size"
      :total="pagination.total"
      @current-change="fetchList"
      class="mt-16px"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import request from '@/config/axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'

/** 素材类型与接口映射 */
const typeApiMap = {
  image: {
    list: '/system/material/image/recycleList',
    restore: '/system/material/image/recycleRestore',
    delete: '/system/material/image/recycleDelete',
    nameField: 'name'
  },
  video: {
    list: '/system/material/video/recycleList',
    restore: '/system/material/video/recycleRestore',
    delete: '/system/material/video/recycleDelete',
    nameField: 'name'
  },
  article: {
    list: '/system/material/article/recycleList',
    restore: '/system/material/article/recycleRestore',
    delete: '/system/material/article/recycleDelete',
    nameField: 'title'
  },
  document: {
    list: '/system/material/document/recycleList',
    restore: '/system/material/document/recycleRestore',
    delete: '/system/material/document/recycleDelete',
    nameField: 'documentName'
  },
  imageText: {
    list: '/system/material/news/recycleList',
    restore: '/system/material/news/recycleRestore',
    delete: '/system/material/news/recycleDelete',
    nameField: 'name'
  }
}

/** 搜索表单数据 */
const searchForm = ref({ name: '', source: '', type: 'image' })
/** 来源选项 */
const sourceOptions = ref<any[]>([])
/** 表格数据 */
const tableData = ref<any[]>([])
/** 分页信息 */
const pagination = ref({ page: 1, size: 10, total: 0 })
/** 选中项 */
const selectedIds = ref<any[]>([])
const isAllSelected = ref(false)

/** 获取回收站列表（根据type动态调用） */
const fetchList = async () => {
  const { type, name, source } = searchForm.value
  const api = typeApiMap[type]
  if (!api) return
  const params: any = {
    pageNo: pagination.value.page,
    pageSize: pagination.value.size
  }
  if (name) params[api.nameField] = name
  if (source) params.sourceOrgId = source
  // 30天内数据由后端自动处理
  const res = await request.get({ url: api.list, params })
  tableData.value = res.data?.list || res.list || []
  pagination.value.total = res.data?.total || res.total || 0
}

const onSearch = () => {
  pagination.value.page = 1
  fetchList()
}
const onReset = () => {
  searchForm.value = { name: '', source: '', type: 'image' }
  pagination.value.page = 1
  fetchList()
}
const onSelectionChange = (rows: any[]) => {
  selectedIds.value = rows.map((r) => r.id)
  isAllSelected.value = rows.length === tableData.value.length && tableData.value.length > 0
}
const onSelectAll = (val: boolean) => {
  if (val) {
    selectedIds.value = tableData.value.map((r) => r.id)
  } else {
    selectedIds.value = []
  }
}
const onRestore = (row: any) => {
  const { type } = searchForm.value
  const api = typeApiMap[type]
  ElMessageBox.confirm('确定要恢复该素材吗？', '提示', { type: 'warning' }).then(async () => {
    await request.post({
      url: api.restore,
      data: [row.id],
      headers: { 'Content-Type': 'application/json' }
    })
    ElMessage.success('恢复成功')
    fetchList()
  })
}
const onDelete = (row: any) => {
  const { type } = searchForm.value
  const api = typeApiMap[type]
  ElMessageBox.confirm('该操作将永久删除素材，是否继续？', '警告', { type: 'error' }).then(
    async () => {
      await request.post({
        url: api.delete,
        data: [row.id],
        headers: { 'Content-Type': 'application/json' }
      })
      ElMessage.success('删除成功')
      fetchList()
    }
  )
}
const onBatchRestore = () => {
  if (!selectedIds.value.length) return
  const { type } = searchForm.value
  const api = typeApiMap[type]
  ElMessageBox.confirm('确定要批量恢复选中素材吗？', '提示', { type: 'warning' }).then(async () => {
    await request.post({
      url: api.restore,
      data: selectedIds.value,
      headers: { 'Content-Type': 'application/json' }
    })
    ElMessage.success('批量恢复成功')
    fetchList()
  })
}
const onBatchDelete = () => {
  if (!selectedIds.value.length) return
  const { type } = searchForm.value
  const api = typeApiMap[type]
  ElMessageBox.confirm('该操作将永久删除选中素材，是否继续？', '警告', { type: 'error' }).then(
    async () => {
      await request.post({
        url: api.delete,
        data: selectedIds.value,
        headers: { 'Content-Type': 'application/json' }
      })
      ElMessage.success('批量删除成功')
      fetchList()
    }
  )
}

// 格式化时间
const formatDate = (val: string) => {
  if (!val) return '-'
  return dayjs(val).format('YYYY-MM-DD HH:mm:ss')
}
// 计算剩余天数
const calcRemainDays = (updateTime: string) => {
  if (!updateTime) return '-'
  const now = dayjs()
  const update = dayjs(updateTime)
  const diff = now.diff(update, 'day')
  const remain = 30 - diff
  return remain > 0 ? remain : 0
}

onMounted(() => {
  fetchList()
  // TODO: 拉取来源选项 sourceOptions
})
</script>

<style scoped lang="scss">
.mb-8px {
  margin-bottom: 8px;
}
.mb-16px {
  margin-bottom: 16px;
}
.mt-16px {
  margin-top: 16px;
}
.batch-actions {
  display: flex;
  align-items: center;
  gap: 0;
}
.batch-actions .el-checkbox {
  margin-right: 24px;
}
.batch-actions .el-button {
  margin-right: 16px;
}
.batch-actions .el-button:last-child {
  margin-right: 0;
}
.search-bar {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}
</style>
