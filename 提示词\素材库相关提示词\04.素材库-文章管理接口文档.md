## 新增文章

**接口地址**:`/system/material/article/create`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
{
  "id": 1,
  "title": "",
  "content": "",
  "sourceOrgId": 0,
  "sourceOrgName": "",
  "categoryId": 0,
  "description": "",
  "visibleOrgId": 0,
  "visibleOrgName": ""
}
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| articleSaveReqVO | 管理后台 - 文章素材新增/更新 Request VO | body | true | ArticleSaveReqVO | ArticleSaveReqVO |
| &emsp;&emsp;id | 文章ID |  | false | integer(int64) |  |
| &emsp;&emsp;title | 文章标题 |  | true | string |  |
| &emsp;&emsp;content | 文章内容 |  | true | string |  |
| &emsp;&emsp;sourceOrgId | 来源机构ID |  | true | integer(int64) |  |
| &emsp;&emsp;sourceOrgName | 来源机构名称 |  | true | string |  |
| &emsp;&emsp;categoryId | 分类ID |  | false | integer(int64) |  |
| &emsp;&emsp;description | 描述 |  | false | string |  |
| &emsp;&emsp;visibleOrgId | 可视范围机构ID |  | false | integer(int64) |  |
| &emsp;&emsp;visibleOrgName | 可视范围机构名称 |  | false | string |  |

**响应状态**:

| 状态码 | 说明 | schema           |
| ------ | ---- | ---------------- |
| 200    | OK   | CommonResultLong |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | integer(int64) | integer(int64) |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": 0,
	"msg": ""
}
```

## 删除文章

**接口地址**:`/system/material/article/delete`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型       | schema |
| -------- | -------- | -------- | -------- | -------------- | ------ |
| id       |          | query    | true     | integer(int64) |        |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 文章详情

**接口地址**:`/system/material/article/detail`

**请求方式**:`GET`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型       | schema |
| -------- | -------- | -------- | -------- | -------------- | ------ |
| id       |          | query    | true     | integer(int64) |        |

**响应状态**:

| 状态码 | 说明 | schema                    |
| ------ | ---- | ------------------------- |
| 200    | OK   | CommonResultArticleRespVO |

**响应参数**:

| 参数名称                   | 参数说明         | 类型              | schema         |
| -------------------------- | ---------------- | ----------------- | -------------- |
| code                       |                  | integer(int32)    | integer(int32) |
| data                       |                  | ArticleRespVO     | ArticleRespVO  |
| &emsp;&emsp;id             | 文章ID           | integer(int64)    |                |
| &emsp;&emsp;title          | 文章标题         | string            |                |
| &emsp;&emsp;sourceOrgId    | 来源机构ID       | integer(int64)    |                |
| &emsp;&emsp;sourceOrgName  | 来源机构名称     | string            |                |
| &emsp;&emsp;categoryId     | 分类ID           | integer(int64)    |                |
| &emsp;&emsp;createTime     | 创建时间         | string(date-time) |                |
| &emsp;&emsp;visibleOrgId   | 可视范围机构ID   | integer(int64)    |                |
| &emsp;&emsp;visibleOrgName | 可视范围机构名称 | string            |                |
| msg                        |                  | string            |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": {
		"id": 1,
		"title": "示例文章",
		"sourceOrgId": 1,
		"sourceOrgName": "内部素材库",
		"categoryId": 1,
		"createTime": "",
		"visibleOrgId": 0,
		"visibleOrgName": ""
	},
	"msg": ""
}
```

## 文章列表

**接口地址**:`/system/material/article/list`

**请求方式**:`GET`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称       | 参数说明               | 请求类型 | 是否必须 | 数据类型 | schema |
| -------------- | ---------------------- | -------- | -------- | -------- | ------ |
| pageNo         | 页码，从 1 开始        | query    | true     | string   |        |
| pageSize       | 每页条数，最大值为 100 | query    | true     | string   |        |
| title          | 文章标题，模糊搜索     | query    | false    | string   |        |
| sourceOrgId    | 来源机构ID             | query    | false    | string   |        |
| categoryId     | 分类ID                 | query    | false    | string   |        |
| visibleOrgId   | 可视范围机构ID         | query    | false    | string   |        |
| visibleOrgName | 可视范围机构名称       | query    | false    | string   |        |

**响应状态**:

| 状态码 | 说明 | schema                              |
| ------ | ---- | ----------------------------------- |
| 200    | OK   | CommonResultPageResultArticleRespVO |

**响应参数**:

| 参数名称 | 参数说明 | 类型 | schema |
| --- | --- | --- | --- |
| code |  | integer(int32) | integer(int32) |
| data |  | PageResultArticleRespVO | PageResultArticleRespVO |
| &emsp;&emsp;list | 数据 | array | ArticleRespVO |
| &emsp;&emsp;&emsp;&emsp;id | 文章ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;title | 文章标题 | string |  |
| &emsp;&emsp;&emsp;&emsp;sourceOrgId | 来源机构ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;sourceOrgName | 来源机构名称 | string |  |
| &emsp;&emsp;&emsp;&emsp;categoryId | 分类ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;createTime | 创建时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;visibleOrgId | 可视范围机构ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;visibleOrgName | 可视范围机构名称 | string |  |
| &emsp;&emsp;total | 总量 | integer(int64) |  |
| msg |  | string |  |

**响应示例**:

```javascript
{
	"code": 0,
	"data": {
		"list": [
			{
				"id": 1,
				"title": "示例文章",
				"sourceOrgId": 1,
				"sourceOrgName": "内部素材库",
				"categoryId": 1,
				"createTime": "",
				"visibleOrgId": 0,
				"visibleOrgName": ""
			}
		],
		"total": 0
	},
	"msg": ""
}
```

## 编辑文章

**接口地址**:`/system/material/article/update`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
{
  "id": 1,
  "title": "",
  "content": "",
  "sourceOrgId": 0,
  "sourceOrgName": "",
  "categoryId": 0,
  "description": "",
  "visibleOrgId": 0,
  "visibleOrgName": ""
}
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| articleSaveReqVO | 管理后台 - 文章素材新增/更新 Request VO | body | true | ArticleSaveReqVO | ArticleSaveReqVO |
| &emsp;&emsp;id | 文章ID |  | false | integer(int64) |  |
| &emsp;&emsp;title | 文章标题 |  | true | string |  |
| &emsp;&emsp;content | 文章内容 |  | true | string |  |
| &emsp;&emsp;sourceOrgId | 来源机构ID |  | true | integer(int64) |  |
| &emsp;&emsp;sourceOrgName | 来源机构名称 |  | true | string |  |
| &emsp;&emsp;categoryId | 分类ID |  | false | integer(int64) |  |
| &emsp;&emsp;description | 描述 |  | false | string |  |
| &emsp;&emsp;visibleOrgId | 可视范围机构ID |  | false | integer(int64) |  |
| &emsp;&emsp;visibleOrgName | 可视范围机构名称 |  | false | string |  |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```
