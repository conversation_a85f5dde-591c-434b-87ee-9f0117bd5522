package cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 考题选项管理保存 Request VO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "考题选项管理保存 Request VO")
public class QuestionOptionSaveReqVO {

    @Schema(description = "主键ID，新增时不传", example = "1")
    private Long id;

    @Schema(description = "考题ID", example = "1", required = true)
    @NotNull(message = "考题ID不能为空")
    private Long questionId;

    @Schema(description = "选项类型：choice-选择项，match_left-匹配左列，match_right-匹配右列", example = "choice", required = true)
    @NotBlank(message = "选项类型不能为空")
    private String optionType;

    @Schema(description = "选项标识，如：A、B、C、D或1、2、3、4", example = "A", required = true)
    @NotBlank(message = "选项标识不能为空")
    private String optionKey;

    @Schema(description = "选项内容", example = "诚实守信", required = true)
    @NotBlank(message = "选项内容不能为空")
    private String optionContent;

    @Schema(description = "是否正确答案：0-否，1-是", example = "1")
    private Integer isCorrect;

    @Schema(description = "排序序号", example = "1")
    private Integer sortOrder;

    @Schema(description = "匹配目标，用于匹配题记录对应关系", example = "1")
    private String matchTarget;
}
