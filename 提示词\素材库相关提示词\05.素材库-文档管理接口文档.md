## 新增文档

**接口地址**:`/system/material/document/create`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
{
  "id": 1,
  "documentName": "",
  "documentUrl": "",
  "sourceOrgId": 0,
  "sourceOrgName": "",
  "categoryId": 0,
  "description": "",
  "visibleOrgId": 0,
  "visibleOrgName": ""
}
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| documentSaveReqVO | 管理后台 - 文档素材新增/更新 Request VO | body | true | DocumentSaveReqVO | DocumentSaveReqVO |
| &emsp;&emsp;id | 文档ID |  | false | integer(int64) |  |
| &emsp;&emsp;documentName | 文档名称 |  | true | string |  |
| &emsp;&emsp;documentUrl | 文档URL |  | true | string |  |
| &emsp;&emsp;sourceOrgId | 来源机构ID |  | true | integer(int64) |  |
| &emsp;&emsp;sourceOrgName | 来源机构名称 |  | true | string |  |
| &emsp;&emsp;categoryId | 分类ID |  | false | integer(int64) |  |
| &emsp;&emsp;description | 描述 |  | false | string |  |
| &emsp;&emsp;visibleOrgId | 可视范围机构ID |  | false | integer(int64) |  |
| &emsp;&emsp;visibleOrgName | 可视范围机构名称 |  | false | string |  |

**响应状态**:

| 状态码 | 说明 | schema           |
| ------ | ---- | ---------------- |
| 200    | OK   | CommonResultLong |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | integer(int64) | integer(int64) |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": 0,
	"msg": ""
}
```

## 删除文档

**接口地址**:`/system/material/document/delete`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型       | schema |
| -------- | -------- | -------- | -------- | -------------- | ------ |
| id       |          | query    | true     | integer(int64) |        |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 文档详情

**接口地址**:`/system/material/document/detail`

**请求方式**:`GET`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型       | schema |
| -------- | -------- | -------- | -------- | -------------- | ------ |
| id       |          | query    | true     | integer(int64) |        |

**响应状态**:

| 状态码 | 说明 | schema                     |
| ------ | ---- | -------------------------- |
| 200    | OK   | CommonResultDocumentRespVO |

**响应参数**:

| 参数名称                   | 参数说明         | 类型              | schema         |
| -------------------------- | ---------------- | ----------------- | -------------- |
| code                       |                  | integer(int32)    | integer(int32) |
| data                       |                  | DocumentRespVO    | DocumentRespVO |
| &emsp;&emsp;id             | 文档ID           | integer(int64)    |                |
| &emsp;&emsp;documentName   | 文档名称         | string            |                |
| &emsp;&emsp;documentUrl    | 文档URL          | string            |                |
| &emsp;&emsp;sourceOrgId    | 来源机构ID       | integer(int64)    |                |
| &emsp;&emsp;sourceOrgName  | 来源机构名称     | string            |                |
| &emsp;&emsp;categoryId     | 分类ID           | integer(int64)    |                |
| &emsp;&emsp;createTime     | 创建时间         | string(date-time) |                |
| &emsp;&emsp;visibleOrgId   | 可视范围机构ID   | integer(int64)    |                |
| &emsp;&emsp;visibleOrgName | 可视范围机构名称 | string            |                |
| msg                        |                  | string            |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": {
		"id": 1,
		"documentName": "示例文档",
		"documentUrl": "https://example.com/document.pdf",
		"sourceOrgId": 1,
		"sourceOrgName": "内部素材库",
		"categoryId": 1,
		"createTime": "",
		"visibleOrgId": 0,
		"visibleOrgName": ""
	},
	"msg": ""
}
```

## 文档列表

**接口地址**:`/system/material/document/list`

**请求方式**:`GET`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称       | 参数说明               | 请求类型 | 是否必须 | 数据类型 | schema |
| -------------- | ---------------------- | -------- | -------- | -------- | ------ |
| pageNo         | 页码，从 1 开始        | query    | true     | string   |        |
| pageSize       | 每页条数，最大值为 100 | query    | true     | string   |        |
| documentName   | 文档名称，模糊搜索     | query    | false    | string   |        |
| sourceOrgId    | 来源机构ID             | query    | false    | string   |        |
| categoryId     | 分类ID                 | query    | false    | string   |        |
| visibleOrgId   | 可视范围机构ID         | query    | false    | string   |        |
| visibleOrgName | 可视范围机构名称       | query    | false    | string   |        |

**响应状态**:

| 状态码 | 说明 | schema                               |
| ------ | ---- | ------------------------------------ |
| 200    | OK   | CommonResultPageResultDocumentRespVO |

**响应参数**:

| 参数名称 | 参数说明 | 类型 | schema |
| --- | --- | --- | --- |
| code |  | integer(int32) | integer(int32) |
| data |  | PageResultDocumentRespVO | PageResultDocumentRespVO |
| &emsp;&emsp;list | 数据 | array | DocumentRespVO |
| &emsp;&emsp;&emsp;&emsp;id | 文档ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;documentName | 文档名称 | string |  |
| &emsp;&emsp;&emsp;&emsp;documentUrl | 文档URL | string |  |
| &emsp;&emsp;&emsp;&emsp;sourceOrgId | 来源机构ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;sourceOrgName | 来源机构名称 | string |  |
| &emsp;&emsp;&emsp;&emsp;categoryId | 分类ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;createTime | 创建时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;visibleOrgId | 可视范围机构ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;visibleOrgName | 可视范围机构名称 | string |  |
| &emsp;&emsp;total | 总量 | integer(int64) |  |
| msg |  | string |  |

**响应示例**:

```javascript
{
	"code": 0,
	"data": {
		"list": [
			{
				"id": 1,
				"documentName": "示例文档",
				"documentUrl": "https://example.com/document.pdf",
				"sourceOrgId": 1,
				"sourceOrgName": "内部素材库",
				"categoryId": 1,
				"createTime": "",
				"visibleOrgId": 0,
				"visibleOrgName": ""
			}
		],
		"total": 0
	},
	"msg": ""
}
```

## 编辑文档

**接口地址**:`/system/material/document/update`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
{
  "id": 1,
  "documentName": "",
  "documentUrl": "",
  "sourceOrgId": 0,
  "sourceOrgName": "",
  "categoryId": 0,
  "description": "",
  "visibleOrgId": 0,
  "visibleOrgName": ""
}
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| documentSaveReqVO | 管理后台 - 文档素材新增/更新 Request VO | body | true | DocumentSaveReqVO | DocumentSaveReqVO |
| &emsp;&emsp;id | 文档ID |  | false | integer(int64) |  |
| &emsp;&emsp;documentName | 文档名称 |  | true | string |  |
| &emsp;&emsp;documentUrl | 文档URL |  | true | string |  |
| &emsp;&emsp;sourceOrgId | 来源机构ID |  | true | integer(int64) |  |
| &emsp;&emsp;sourceOrgName | 来源机构名称 |  | true | string |  |
| &emsp;&emsp;categoryId | 分类ID |  | false | integer(int64) |  |
| &emsp;&emsp;description | 描述 |  | false | string |  |
| &emsp;&emsp;visibleOrgId | 可视范围机构ID |  | false | integer(int64) |  |
| &emsp;&emsp;visibleOrgName | 可视范围机构名称 |  | false | string |  |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```
