package cn.bztmaster.cnt.module.publicbiz.dal.mysql.question;

import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.QuestionCategoryListReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionCategoryDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 考题分类表 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface QuestionCategoryMapper extends BaseMapperX<QuestionCategoryDO> {

    default List<QuestionCategoryDO> selectList(QuestionCategoryListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<QuestionCategoryDO>()
                .likeIfPresent(QuestionCategoryDO::getLevel1Name, reqVO.getLevel1Name())
                .likeIfPresent(QuestionCategoryDO::getLevel2Name, reqVO.getLevel2Name())
                .likeIfPresent(QuestionCategoryDO::getLevel3Name, reqVO.getLevel3Name())
                .eqIfPresent(QuestionCategoryDO::getBiz, reqVO.getBiz())
                .eqIfPresent(QuestionCategoryDO::getParentId, reqVO.getParentId())
                .eqIfPresent(QuestionCategoryDO::getLevel, reqVO.getLevel())
                .eqIfPresent(QuestionCategoryDO::getStatus, reqVO.getStatus())
                .orderByAsc(QuestionCategoryDO::getSortOrder)
                .orderByDesc(QuestionCategoryDO::getId));
    }
}
