---
description: 
globs: 
alwaysApply: false
---
参考附件截图实现素材库的界面设计，路径：src\views\infra\materiallibrary\index.vue
 请注意:来源是下拉框单选，固定值（请选择、来源A、来源B）
 点击重置时，清空 素材名称文本框的值和来源下拉框的选项
 列表需要分页展示


 请按照附件截图调整素材库界面的设计，界面整体布局可以参考用户管理界面system/user。目前存在以下问题：1. 左侧新增分来菜单栏没实现 2. 右侧的切换tab没实现，切换选项包括：图片、视频、文章、文档、图文，请先实现当前选择的 图片tab界面  


 @materiallibrary 这个目录下的文件需要做以下调整：1. index页面的新增图片和列表的编辑是用的同一个界面，所以需要把新增图片的弹窗界面代码迁移到src\views\infra\materiallibrary\components\AddPicture.vue 文件中，确保点击index.vue界面的新增图片和列表操作栏的编辑按钮时弹窗显示AddPicture.vue界面


 @AddMaterial.vue 按照上面迁移代码的方式，请继续完成index.vue界面的文章、文档以及图文的相关代码迁移，迁移对应界面如下:
1.将 AddMaterial.vue 中与文章相关的弹窗代码迁移到 src\views\infra\materiallibrary\components\AddArticle.vue，只保留文章相关表单与逻辑
2.将 AddMaterial.vue 中与文档相关的弹窗代码迁移到 src\views\infra\materiallibrary\components\AddDocument.vue，只保留文档相关表单与逻辑
3.将 AddMaterial.vue 中与图文相关的弹窗代码迁移到 src\views\infra\materiallibrary\components\AddImageText.vue，只保留图文相关表单与逻辑
4.确保index.vue中文章tab、文档tab、图文tab的新增按钮和数据列表的编辑按钮点击后弹窗展示对应界面


新增分类的下拉框的可见范围下拉数据源绑定，要求如下
1. 请求接口地址：/system/dept/simple-list,参考用户管理界面的接口请求 /system/dept/simple-list
   返回数据案例：
   {
  "code": 0,
  "data": [
    {
      "id": 100,
      "name": "川能投总机构",
      "parentId": 0
    },
    {
      "id": 115,
      "name": "川能投分机构",
      "parentId": 0
    },
    {
      "id": 101,
      "name": "机构A",
      "parentId": 100
    },
    {
      "id": 103,
      "name": "研发部门",
      "parentId": 101
    },
    {
      "id": 108,
      "name": "市场部门",
      "parentId": 102
    },
    {
      "id": 116,
      "name": "分机构A",
      "parentId": 115
    },
    {
      "id": 102,
      "name": "机构B",
      "parentId": 100
    },
    {
      "id": 104,
      "name": "市场部门",
      "parentId": 101
    },
    {
      "id": 109,
      "name": "财务部门",
      "parentId": 102
    },
    {
      "id": 105,
      "name": "测试部门",
      "parentId": 101
    },
    {
      "id": 106,
      "name": "财务部门",
      "parentId": 101
    },
    {
      "id": 107,
      "name": "运维部门",
      "parentId": 101
    }
  ],
  "msg": ""
}
2. 数据值绑定只需要绑定返回结果集中，parentId=0的 或者子集的parentId等于 parentId=0所对应的id的数据，例如案例中 某某部门是不需要绑定的，只需要展示某某机构。按照父子级以树形的方式加载绑定。
3.只允许单选，支持根据名称搜索父子级的数据，方便数据较多时快速查找



目前对于分类没有单独做维护的菜单，所以需要调整index.vue左侧分类模块的功能：
1.在分类的名称后面增加一个编辑、删除按钮
2.点击编辑时弹出 新增分类的弹窗，将标题改成 编辑分类
3. 点击删除按钮时，弹出删除提示确认框