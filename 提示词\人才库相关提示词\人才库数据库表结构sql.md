## 1. 人才库中枢（OneID）相关表

## 1.1. 用户主表（talent_user）
```sql
CREATE TABLE `talent_user` (
  `user_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `name` VARCHAR(50) NOT NULL COMMENT '姓名',
  `identity_id` VARCHAR(18) NOT NULL UNIQUE COMMENT '身份证号',
  `birth_date` DATE NOT NULL COMMENT '出生日期',
  `gender` VARCHAR(10) COMMENT '性别（可选值：男、女、其他）',
  `phone` VARCHAR(20) NOT NULL UNIQUE COMMENT '手机号',
  `email` VARCHAR(100) COMMENT '邮箱',
  `avatar_url` VARCHAR(255) COMMENT '头像URL',
  `status` VARCHAR(30) COMMENT 'OneID状态（可选值：正常、待合并、已禁用）',
  `register_source` VARCHAR(50) COMMENT '用户来源',
  `oneid` CHAR(36) NOT NULL DEFAULT '' COMMENT 'OneID GUID',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`user_id`)
) COMMENT='平台用户主表';


ALTER TABLE `talent_user`
    ADD COLUMN `completeness` TINYINT DEFAULT 0 COMMENT '档案完整度百分比';

ALTER TABLE `talent_user` ADD COLUMN
`tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号'

```

## 1.2 教育背景表（talent_education）
```sql
CREATE TABLE `talent_education` (
  `education_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `institution` VARCHAR(100) NOT NULL COMMENT '毕业院校',
  `college_address` VARCHAR(255) COMMENT '学院地址',
  `degree_type` VARCHAR(20) COMMENT '学位类型（可选值：专科、本科、硕士、博士）',
  `major` VARCHAR(100) COMMENT '专业',
  `start_date` DATE COMMENT '入学时间',
  `end_date` DATE COMMENT '毕业时间',
  `academic_ranking` VARCHAR(50) COMMENT '学业排名',
  `is_internship` TINYINT(1) DEFAULT 0 COMMENT '是否实习 0-否 1-是',
  `internship_type` VARCHAR(20) COMMENT '实习类别（可选值：生产实习、认知实习、毕业实习、其他）',
  `internship_duration` INT COMMENT '实习时长（天）',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='教育背景';
```

### 1.3 校园实践表（talent_campus_practice）
```sql
CREATE TABLE `talent_campus_practice` (
  `practice_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `practice_name` VARCHAR(100) NOT NULL COMMENT '实践名称',
  `organizer` VARCHAR(100) COMMENT '组织方',
  `start_date` DATE COMMENT '开始时间',
  `end_date` DATE COMMENT '结束时间',
  `practice_report` TEXT COMMENT '实践报告/总结',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='校园实践';
```

### 1.4 校园实践表（talent_campus_practice）
```sql
CREATE TABLE `talent_campus_practice` (
  `practice_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `practice_name` VARCHAR(100) NOT NULL COMMENT '实践名称',
  `organizer` VARCHAR(100) COMMENT '组织方',
  `start_date` DATE COMMENT '开始时间',
  `end_date` DATE COMMENT '结束时间',
  `practice_report` TEXT COMMENT '实践报告/总结',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='校园实践';
```

### 1.5 实习经历表（talent_internship）
```sql
CREATE TABLE `talent_internship` (
  `internship_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `company` VARCHAR(100) NOT NULL COMMENT '实习公司',
  `position` VARCHAR(100) COMMENT '实习岗位',
  `start_date` DATE COMMENT '开始时间',
  `end_date` DATE COMMENT '结束时间',
  `responsibilities` TEXT COMMENT '工作职责',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='实习经历';
```

### 1.6 项目经历表（talent_project）
```sql
CREATE TABLE `talent_project` (
  `project_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `name` VARCHAR(100) NOT NULL COMMENT '项目名称',
  `description` TEXT COMMENT '项目描述',
  `start_date` DATE COMMENT '开始时间',
  `end_date` DATE COMMENT '结束时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='项目经历';
```

### 1.7 培训记录表（talent_training）
```sql
CREATE TABLE `talent_training` (
  `training_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `provider` VARCHAR(100) COMMENT '培训机构',
  `course` VARCHAR(100) COMMENT '课程名称',
  `complete_date` DATE COMMENT '完成日期',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='培训记录';
```

### 1.8 技能清单表（talent_skill）
```sql
CREATE TABLE `talent_skill` (
  `skill_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `name` VARCHAR(100) NOT NULL COMMENT '技能名称',
  `level` VARCHAR(10) COMMENT '掌握程度（可选值：了解、熟悉、精通）',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='技能清单';
```

### 1.9 认证与资质表（talent_certificate）
```sql
CREATE TABLE `talent_certificate` (
  `certificate_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `name` VARCHAR(100) NOT NULL COMMENT '证书名称',
  `issuer` VARCHAR(100) COMMENT '发证机构',
  `issue_date` DATE COMMENT '颁发日期',
  `source` VARCHAR(20) COMMENT '记录来源（可选值：PLATFORM、AGENCY、SELF）',
  `status` VARCHAR(30) COMMENT '审核状态（可选值：VERIFIED、PENDING_VERIFICATION、REJECTED）',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='认证与资质';
```

### 1.10 求职记录表（talent_job_application）
```sql
CREATE TABLE `talent_job_application` (
  `application_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `company` VARCHAR(100) NOT NULL COMMENT '申请公司',
  `position` VARCHAR(100) COMMENT '申请职位',
  `apply_date` DATE COMMENT '申请日期',
  `status` VARCHAR(30) COMMENT '状态（可选值：已投递、面试中、已录用、未通过）',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='求职记录';
```

### 1.11 工作履历表（talent_employment）
```sql
CREATE TABLE `talent_employment` (
  `employment_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `company` VARCHAR(100) NOT NULL COMMENT '就职公司',
  `position` VARCHAR(100) COMMENT '职位',
  `start_date` DATE COMMENT '入职时间',
  `end_date` DATE COMMENT '离职时间',
  `salary` DECIMAL(10,2) COMMENT '薪资',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='工作履历';
```
### 1.12 人才标签表（talent_user_tag）
```sql
CREATE TABLE `talent_user_tag` (
  `user_tag_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `tag_id` BIGINT NOT NULL COMMENT '标签ID，关联标签库',
  `tag_type_id` BIGINT NOT NULL COMMENT '标签类型ID',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='用户标签关联表';
```
### 1.13 标签类型表（talent_tag_type）
```sql
CREATE TABLE `talent_tag_type` (
  `tag_type_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '标签类型ID',
  `type_code` VARCHAR(50) NOT NULL UNIQUE COMMENT '标签类型编码',
  `type_name` VARCHAR(100) NOT NULL COMMENT '标签类型名称',
  `description` VARCHAR(255) COMMENT '描述',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
) COMMENT='标签类型表';
```

## 1.14. 标签库表（talent_tag_library）
```sql
CREATE TABLE `talent_tag_library` (
  `tag_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '标签ID',
  `tag_type_id` BIGINT NOT NULL COMMENT '标签类型ID',
  `tag_code` VARCHAR(50) NOT NULL UNIQUE COMMENT '标签编码',
  `tag_name` VARCHAR(100) NOT NULL COMMENT '标签名称',
  `description` VARCHAR(255) COMMENT '标签描述',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'

) COMMENT='标签库表';




ALTER TABLE `talent_education` ADD COLUMN
    `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号';

ALTER TABLE `talent_campus_practice` ADD COLUMN
    `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号';

ALTER TABLE `talent_internship` ADD COLUMN
    `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号';

ALTER TABLE `talent_project` ADD COLUMN
    `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号';
ALTER TABLE `talent_training` ADD COLUMN
    `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号';

ALTER TABLE `talent_skill` ADD COLUMN
    `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号';
ALTER TABLE `talent_certificate` ADD COLUMN
    `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号';


ALTER TABLE `talent_job_application` ADD COLUMN
    `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号';
ALTER TABLE `talent_employment` ADD COLUMN
    `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号';


ALTER TABLE `talent_user_tag` ADD COLUMN
    `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号';
ALTER TABLE `talent_tag_type` ADD COLUMN
    `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号';
ALTER TABLE `talent_tag_library` ADD COLUMN
    `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号';

```

### 1.15 用户评价表（talent_user_comment）
```sql
CREATE TABLE `talent_user_comment` (
  `comment_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '被评价用户ID',
  `role` VARCHAR(20) NOT NULL COMMENT '评价角色（雇主/培训师等）',
  `reviewer_name` VARCHAR(50) COMMENT '评价人姓名',
  `service_type` VARCHAR(50) COMMENT '服务类型：订单、课程、其它',
  `service_name` VARCHAR(100) COMMENT '服务名称：订单号、课程名称、其它',
  `content` TEXT COMMENT '评价内容',
  `score` TINYINT NOT NULL DEFAULT 5 COMMENT '评分（1-5星）',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号'
) COMMENT='用户评价表';
```