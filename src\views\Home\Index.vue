<template>
  <div class="dashboard-container">
    <div class="dashboard-header">
      <el-icon><HomeFilled /></el-icon>
      <span class="dashboard-title">个人工作台</span>
      <span class="dashboard-date">2024年6月19日 星期三 下午15:00</span>
    </div>
    <div class="dashboard-main">
      <div class="dashboard-row">
        <el-card class="dashboard-card kpi-card">
          <div class="kpi-title">核心指标</div>
          <div class="kpi-list">
            <div class="kpi-item">
              <div class="kpi-value">24</div>
              <div class="kpi-label">今日新增线索</div>
              <div class="kpi-trend up">↑ 12% 较昨日</div>
            </div>
            <div class="kpi-item">
              <div class="kpi-value">8</div>
              <div class="kpi-label">待跟进商机</div>
              <div class="kpi-trend down">↓ 3% 较昨日</div>
            </div>
            <div class="kpi-item">
              <div class="kpi-value">16</div>
              <div class="kpi-label">待处理订单</div>
              <div class="kpi-trend up">↑ 8% 较昨日</div>
            </div>
            <div class="kpi-item">
              <div class="kpi-value">12,534</div>
              <div class="kpi-label">人才库数量</div>
              <div class="kpi-trend up">↑ 1.5% 较上周</div>
            </div>
          </div>
        </el-card>
        <el-card class="dashboard-card quick-card">
          <div class="quick-title">快速入口</div>
          <div class="quick-list">
            <div class="quick-item" v-for="(item, idx) in quickBtns" :key="idx">
              <el-icon><component :is="item.icon" /></el-icon>
              {{ item.label }}
            </div>
          </div>
        </el-card>
      </div>
      <div class="dashboard-row dashboard-row-flex">
        <el-card class="dashboard-card todo-card wide">
          <div class="todo-title">我的待办</div>
          <el-table :data="todoList" border style="width: 100%" size="small">
            <el-table-column prop="title" label="事项" min-width="180" />
            <el-table-column prop="owner" label="负责人" width="80" />
            <el-table-column prop="time" label="时间" width="140" />
            <el-table-column prop="type" label="类型" width="120" />
            <el-table-column prop="priority" label="优先级" width="100">
              <template #default="{ row }">
                <el-tag :type="row.priority === '高' ? 'danger' : row.priority === '中' ? 'warning' : 'info'">
                  {{ row.priority === '高' ? '高优先级' : row.priority === '中' ? '中优先级' : '低优先级' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
        <el-card class="dashboard-card schedule-card narrow">
          <div class="schedule-title">日程安排</div>
          <el-calendar v-model="calendarDate" :fullscreen="false" />
          <div class="schedule-list">
            <div class="schedule-item"><span class="dot blue"></span>15:30 高校实践项目评审会 3号楼201会议室</div>
            <div class="schedule-item"><span class="dot green"></span>16:40 月度运营数据分析报告</div>
          </div>
        </el-card>
      </div>
    </div>
    <el-card class="dashboard-card notice-card">
      <div class="notice-title">系统公告</div>
      <div class="notice-list">
        <div class="notice-item" v-for="item in noticeList" :key="item.id">
          <div class="notice-header">
            <el-icon class="notice-icon" :style="{ color: item.iconColor }">
              <component :is="item.icon" />
            </el-icon>
            <span class="notice-main-title">{{ item.title }}</span>
          </div>
          <div class="notice-meta">
            <span>{{ item.date }}</span>
            <span>发布人：{{ item.author }}</span>
          </div>
          <div class="notice-content">{{ item.content }}</div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { HomeFilled, Plus, Document, UserFilled, Memo, DataAnalysis, Setting, QuestionFilled, BellFilled, StarFilled, Present } from '@element-plus/icons-vue'

const calendarDate = ref(new Date())
const todoList = [
  { title: '审批 XX大学实践项目合作协议', owner: '李四', time: '今天 16:00前', type: '高校实践', priority: '高' },
  { title: '处理李女士的月嫂服务投诉', owner: '王五', time: '今天 17:30前', type: '家政服务', priority: '高' },
  { title: '审核6月金牌月嫂认证考试结果', owner: '系统自动', time: '明天 10:00前', type: '培训认证', priority: '中' },
  { title: '确认YY企业培训订单收款', owner: '赵六', time: '明天 19:00前', type: '企业培训', priority: '中' }
]
const noticeList = [
  {
    id: 1,
    icon: BellFilled,
    iconColor: '#faad14',
    title: '系统维护通知',
    date: '2024年6月18日',
    author: '系统管理员',
    content: '平台将于2024年6月22日(周六)凌晨2:00-4:00进行系统维护，届时所有服务将暂停使用。请提前做好工作安排，给您带来不便敬请谅解。'
  },
  {
    id: 2,
    icon: Present,
    iconColor: '#f39c12',
    title: '端午节放假通知',
    date: '2024年6月17日',
    author: '行政部',
    content: '根据国家法定假日安排，公司端午节放假时间为2024年6月22日(周六)至6月24日(周一)，共3天。6月25日(周二)正常上班。节假日期间，请各部门安排好值班人员。'
  },
  {
    id: 3,
    icon: StarFilled,
    iconColor: '#faad14',
    title: 'OneID人才库功能升级',
    date: '2024年6月15日',
    author: '产品部',
    content: '人才库中根(OneID)已完成重大升级，新增跨场景用户识别功能，支持自动合并用户在不同业务场景中的数据。详细更新内容请查看产品更新日志。'
  }
]
const quickBtns = [
  { icon: Plus, label: '新建订单' },
  { icon: Document, label: '发布课程' },
  { icon: UserFilled, label: '客户管理' },
  { icon: Memo, label: '合同管理' },
  { icon: DataAnalysis, label: '数据报告' },
  { icon: UserFilled, label: '新建用户' },
  { icon: Setting, label: '系统设置' },
  { icon: QuestionFilled, label: '帮助中心' }
]
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
  background: #f5f6fa;
}
.dashboard-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}
.dashboard-title {
  font-size: 22px;
  font-weight: bold;
  margin-left: 8px;
}
.dashboard-date {
  font-size: 15px;
  color: #888;
}
.dashboard-main {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.dashboard-row {
  display: flex;
  gap: 16px;
}
.dashboard-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px #f0f1f2;
  flex: 1;
}
.kpi-title, .quick-title, .todo-title, .schedule-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 12px;
}
.kpi-list {
  display: flex;
  gap: 24px;
}
.kpi-item {
  flex: 1;
  background: #f8fafc;
  border-radius: 6px;
  padding: 16px 12px;
  text-align: center;
}
.kpi-value {
  font-size: 28px;
  font-weight: bold;
  color: #409eff;
}
.kpi-label {
  font-size: 15px;
  color: #666;
  margin: 6px 0 2px 0;
}
.kpi-trend {
  font-size: 13px;
}
.kpi-trend.up {
  color: #13ce66;
}
.kpi-trend.down {
  color: #f56c6c;
}
.quick-list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 18px 18px;
  margin-top: 8px;
}
.quick-item {
  width: 100%;
  aspect-ratio: 1/1;
  height: 80px;
  background: #f8fafc;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #409eff;
  cursor: pointer;
  gap: 8px;
  transition: background 0.2s;
  box-shadow: 0 1px 4px #f0f1f2;
}
.quick-item:hover {
  background: #e6f7ff;
}
.todo-title, .schedule-title {
  margin-bottom: 10px;
}
.schedule-list {
  margin-top: 12px;
}
.schedule-item {
  font-size: 14px;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
}
.dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
}
.dot.blue {
  background: #409eff;
}
.dot.green {
  background: #13ce66;
}
.notice-card {
  margin-top: 24px;
}
.notice-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 16px;
}
.notice-list {
  display: flex;
  flex-direction: column;
  gap: 18px;
}
.notice-item {
  background: #f8fafc;
  border-radius: 6px;
  padding: 16px 18px 12px 18px;
  box-shadow: 0 1px 4px #f0f1f2;
}
.notice-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 6px;
}
.notice-icon {
  font-size: 20px;
}
.notice-main-title {
  font-weight: bold;
}
.notice-meta {
  font-size: 13px;
  color: #888;
  margin-bottom: 6px;
  display: flex;
  gap: 18px;
}
.notice-content {
  font-size: 14px;
  color: #333;
  line-height: 1.7;
}
.dashboard-row-flex {
  display: flex;
  gap: 16px;
}
.todo-card.wide {
  flex: 1.7;
}
.schedule-card.narrow {
  flex: 1.5;
  min-width: 420px;
  max-width: 480px;
}
</style>
