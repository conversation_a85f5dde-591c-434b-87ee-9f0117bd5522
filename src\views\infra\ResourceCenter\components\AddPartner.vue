<!--
  页面名称：新增合作伙伴
  功能描述：新增合作伙伴，分组表单，支持多类型字段、文件上传、评分、分组标题、两列布局、底部按钮
-->
<template>
  <el-drawer
    v-model="visible"
    :title="drawerTitle"
    size="600px"
    :with-header="true"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="110px"
      label-position="top"
      class="partner-form"
    >
      <!-- 机构基本信息 -->
      <div class="form-section">
      <div class="form-group-title">机构基本信息</div>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="机构全称" prop="name" required>
            <el-input v-model="form.name" placeholder="必填, 机构全称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="机构简称" prop="shortName">
            <el-input v-model="form.shortName" placeholder="机构简称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="机构类型" prop="type" required>
            <el-select v-model="form.type" placeholder="请选择">
              <el-option v-for="item in orgTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- 新增业务模块下拉框 -->
        <el-col :span="12">
          <el-form-item label="业务模块" prop="bizModule">
            <el-select v-model="form.bizModule" placeholder="请选择">
              <el-option v-for="item in bizModuleOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合作状态" prop="status" required>
            <el-select v-model="form.status" placeholder="请选择">
              <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="法人代表" prop="legalPerson">
            <el-input v-model="form.legalPerson" placeholder="法定代表人姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="成立日期" prop="foundationDate">
            <el-date-picker v-model="form.foundationDate" type="date" placeholder="年/月/日" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="统一社会信用代码" prop="creditCode">
            <el-input v-model="form.creditCode" placeholder="统一社会信用代码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="注册地址" prop="registerAddress">
            <el-input v-model="form.registerAddress" placeholder="注册地址" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="经营地址" prop="businessAddress">
            <el-input v-model="form.businessAddress" placeholder="经营地址" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="主营业务" prop="mainBusiness">
        <el-input v-model="form.mainBusiness" type="textarea" :rows="2" placeholder="主营业务描述" />
      </el-form-item>
      </div>

      <!-- 合作与商业信息 -->
      <div class="form-section">
      <div class="form-group-title">合作与商业信息</div>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="主要联系人" prop="contactName">
            <el-input v-model="form.contactName" placeholder="主要联系人姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="contactPhone">
            <el-input v-model="form.contactPhone" placeholder="联系电话" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="当前评级" prop="rating">
            <el-rate v-model="form.rating" :max="5" show-score />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合作模式" prop="cooperationMode">
            <el-select v-model="form.cooperationMode" placeholder="请选择">
              <el-option label="项目合作" value="项目合作" />
              <el-option label="渠道合作" value="渠道合作" />
              <el-option label="战略合作" value="战略合作" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="我方签约人" prop="signer">
            <el-select v-model="form.signer" placeholder="请选择" clearable>
              <el-option
                v-for="item in signerOptions"
                :key="item.id"
                :label="item.nickname"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同编号" prop="contractNo">
            <el-input v-model="form.contractNo" placeholder="合同编号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同开始日期" prop="contractStart">
            <el-date-picker v-model="form.contractStart" type="date" placeholder="年/月/日" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同到期日期" prop="contractEnd">
            <el-date-picker v-model="form.contractEnd" type="date" placeholder="年/月/日" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="保证金" prop="deposit">
            <el-input v-model="form.deposit" placeholder="保证金金额" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="续约提醒日期" prop="renewDate">
            <el-date-picker v-model="form.renewDate" type="date" placeholder="年/月/日" style="width: 100%" />
          </el-form-item>
        </el-col>
      </el-row>
      </div>

      <!-- 资质与结算信息 -->
      <div class="form-section">
      <div class="form-group-title">资质与结算信息</div>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="对公账户名" prop="accountName">
            <el-input v-model="form.accountName" placeholder="对公账户名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结算周期" prop="settlementCycle">
            <el-select v-model="form.settlementCycle" placeholder="请选择">
              <el-option label="月结" value="月结" />
              <el-option label="季结" value="季结" />
              <el-option label="年结" value="年结" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开户银行" prop="bankName">
            <el-input v-model="form.bankName" placeholder="开户银行名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="银行账号" prop="bankAccount">
            <el-input v-model="form.bankAccount" placeholder="银行账号" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="资质文件" prop="qualificationFile">
        <el-upload
          class="upload-demo"
          drag
          :show-file-list="true"
          :auto-upload="false"
          :on-change="handleFileChange"
          :on-preview="previewFile"
          :file-list="form.qualificationFile"
        >
          <el-button>选择文件</el-button>
          <template #tip>
            <div class="el-upload__tip">未选择任何文件</div>
          </template>
        </el-upload>
      </el-form-item>
      </div>

      <!-- 开票信息 -->
      <div class="form-section">
      <div class="form-group-title form-group-title-blue">开票信息</div>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="开票类型" prop="invoiceType">
            <el-select v-model="form.invoiceType" placeholder="请选择">
              <el-option label="企业" value="企业" />
              <el-option label="个人" value="个人" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开票名称" prop="invoiceName">
            <el-input v-model="form.invoiceName" placeholder="开票抬头名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="纳税人识别号" prop="taxId">
            <el-input v-model="form.taxId" placeholder="统一社会信用代码或税号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="社会组织代码" prop="orgCode">
            <el-input v-model="form.orgCode" placeholder="社会组织代码（如适用）" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开票地址" prop="invoiceAddress">
            <el-input v-model="form.invoiceAddress" placeholder="开票地址" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开票电话" prop="invoicePhone">
            <el-input v-model="form.invoicePhone" placeholder="开票联系电话" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开户银行" prop="invoiceBank">
            <el-input v-model="form.invoiceBank" placeholder="开户银行名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="银行账号" prop="invoiceBankAccount">
            <el-input v-model="form.invoiceBankAccount" placeholder="银行账号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开票邮箱" prop="invoiceEmail">
            <el-input v-model="form.invoiceEmail" placeholder="开票邮箱地址" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开票联系人" prop="invoiceContact">
            <el-input v-model="form.invoiceContact" placeholder="开票联系人姓名" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="开票资质文件" prop="invoiceQualificationFile">
        <el-upload
          class="upload-demo"
          drag
          :show-file-list="true"
          :auto-upload="false"
          :on-change="handleInvoiceFileChange"
          :on-preview="previewFile"
          :file-list="form.invoiceQualificationFile"
        >
          <el-button>选择文件</el-button>
          <template #tip>
            <div class="el-upload__tip">未选择任何文件</div>
          </template>
        </el-upload>
      </el-form-item>
      <el-form-item label="开票备注" prop="invoiceRemark">
        <el-input v-model="form.invoiceRemark" type="textarea" :rows="2" placeholder="开票要求、注意事项等" />
      </el-form-item>
      </div>

      <!-- 底部按钮 -->
      <div class="form-footer">
        <el-button @click="onCancel">取消</el-button>
        <el-button type="primary" @click="onSubmit">保存</el-button>
      </div>
    </el-form>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, defineEmits, watch } from 'vue'
import { createPartner, createPartnerLog, updatePartner, uploadFile } from '@/api/infra/business/partner'
import { useUserStore } from '@/store/modules/user'
import { ElMessage } from 'element-plus'
import type { UploadUserFile } from 'element-plus'
import { getUserPage } from '@/api/system/user'
import request from '@/config/axios'
import { getDictDataPage } from '@/api/system/dict/dict.data'
import { getSimpleDeptList } from '@/api/system/dept'

const userStore = useUserStore()

const visible = ref(false)
const formRef = ref()
const emit = defineEmits(['success'])

const mode = ref('add')
const partner = ref<any>(null)

const drawerTitle = ref('新增合作伙伴')
watch(mode, (val) => {
  drawerTitle.value = val === 'edit' ? '编辑合作伙伴' : '新增合作伙伴'
})

// 表单数据
const form = reactive({
  name: '',
  shortName: '',
  type: '',
  bizModule: '', // 新增业务模块字段
  status: '',
  legalPerson: '',
  foundationDate: '',
  creditCode: '',
  registerAddress: '',
  businessAddress: '',
  mainBusiness: '',
  contactName: '',
  contactPhone: '',
  rating: 0,
  cooperationMode: '',
  signer: undefined as number | undefined,
  contractNo: '',
  contractStart: '',
  contractEnd: '',
  deposit: '',
  renewDate: '',
  accountName: '',
  settlementCycle: '',
  bankName: '',
  bankAccount: '',
  qualificationFile: [] as UploadUserFile[],
  invoiceType: '',
  invoiceName: '',
  taxId: '',
  orgCode: '',
  invoiceAddress: '',
  invoicePhone: '',
  invoiceBank: '',
  invoiceBankAccount: '',
  invoiceEmail: '',
  invoiceContact: '',
  invoiceQualificationFile: [] as UploadUserFile[],
  invoiceRemark: ''
})

// 校验规则
const rules = {
  name: [{ required: true, message: '请输入机构全称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择机构类型', trigger: 'change' }],
  status: [{ required: true, message: '请选择合作状态', trigger: 'change' }],
  bizModule: [{ required: true, message: '请选择业务模块', trigger: 'change' }], // 新增必填校验
}

// 文件上传事件
const handleFileChange = async (file: UploadUserFile) => {
  if (file && file.raw) {
    const res = await uploadFile(file.raw)
    if (res) {
      form.qualificationFile = [{ name: file.name, url: res }]
      ElMessage.success('上传成功')
    } else {
      ElMessage.error('上传失败')
    }
  }
}
const handleInvoiceFileChange = async (file: UploadUserFile) => {
  if (file && file.raw) {
    const res = await uploadFile(file.raw)
    if (res) {
      form.invoiceQualificationFile = [{ name: file.name, url: res }]
      ElMessage.success('上传成功')
    } else {
      ElMessage.error('上传失败')
    }
  }
}
// 文件预览/下载
const previewFile = (file: any) => {
  window.open(file.url, '_blank')
}

// 签约人选项
const signerOptions = ref<{ id: number; nickname: string }[]>([])

const loadSignerOptions = async () => {

  const params = { pageNo: 1, pageSize: 20, status: 0 }
  const res = await getUserPage(params)

  const list = res.list ?? []
  // 关键：slice拷贝触发响应式
  signerOptions.value = list.map((item: any) => ({ id: item.id, nickname: item.nickname })).slice()

}

const orgTypeOptions = ref<{ label: string; value: string }[]>([])

const loadOrgTypeOptions = async () => {
  const res = await getDictDataPage({ dictType: 'institutional_type', pageNo: 1, pageSize: 50 })

  const list = res.list ?? []
  orgTypeOptions.value = list.map((item: any) => ({ label: item.label, value: item.value }))
}

const statusOptions = ref<{ label: string; value: string }[]>([])

const loadStatusOptions = async () => {
  const res = await getDictDataPage({ dictType: 'cooperative_status', pageNo: 1, pageSize: 50 })
  const list = res?.list ?? []
  statusOptions.value = list.map((item: any) => ({ label: item.label, value: item.value }))
}

const bizModuleOptions = ref<{ label: string; value: string }[]>([])
const loadBizModuleOptions = async () => {
  const res = await getDictDataPage({ dictType: 'business_module', pageNo: 1, pageSize: 50 })
  const list = res?.list ?? []
  bizModuleOptions.value = list.map((item: any) => ({ label: item.label, value: item.value }))
}

// 取消
const onCancel = () => {
  visible.value = false
}
// 提交
const onSubmit = async () => {
  formRef.value.validate(async (valid: boolean) => {
    if (!valid) return
    // 1. 构造接口文档要求的字段
    const partnerData = {
      ...(mode.value === 'edit' && partner.value && partner.value.id ? { id: partner.value.id } : {}),
      name: form.name,
      shortName: form.shortName,
      type: form.type,
      biz: form.bizModule, // 这里用bizModule
      status: form.status,
      risk: '', // 风险等级，表单无，留空
      owner: form.signer ? String(form.signer) : '0',
      ownerName: form.signer ? (signerOptions.value.find(item => item.id === form.signer)?.nickname || '') : '',
      legalPerson: form.legalPerson,
      foundationDate: form.foundationDate,
      creditCode: form.creditCode,
      registerAddress: form.registerAddress,
      businessAddress: form.businessAddress,
      mainBusiness: form.mainBusiness,
      contactName: form.contactName,
      contactPhone: form.contactPhone,
      rating: form.rating,
      cooperationMode: form.cooperationMode,
      contractNo: form.contractNo,
      contractStart: form.contractStart,
      contractEnd: form.contractEnd,
      deposit: form.deposit,
      renewDate: form.renewDate,
      accountName: form.accountName,
      settlementCycle: form.settlementCycle,
      bankName: form.bankName,
      bankAccount: form.bankAccount,
      qualificationFile: form.qualificationFile?.[0]?.url || '',
      invoiceType: form.invoiceType,
      invoiceName: form.invoiceName,
      taxId: form.taxId,
      orgCode: form.orgCode,
      invoiceAddress: form.invoiceAddress,
      invoicePhone: form.invoicePhone,
      invoiceBank: form.invoiceBank,
      invoiceBankAccount: form.invoiceBankAccount,
      invoiceEmail: form.invoiceEmail,
      invoiceContact: form.invoiceContact,
      invoiceQualificationFile: form.invoiceQualificationFile?.[0]?.url || '',
      invoiceRemark: form.invoiceRemark
    }
    try {
      let res
      if (mode.value === 'edit') {
        res = await updatePartner(partnerData)
      } else {
        res = await createPartner(partnerData)
      }
      if (res) {
        ElMessage.success(mode.value === 'edit' ? '更新成功' : '新增成功')
        visible.value = false
        emit('success')
        return
      }
      throw new Error(res.msg || (mode.value === 'edit' ? '更新失败' : '新增失败'))
    } catch (e: any) {
      ElMessage.error(e.message || '操作失败')
    }
  })
}

const open = async (_mode = 'add', partnerData: any = null) => {
  mode.value = _mode
  partner.value = partnerData
  // 重置表单字段
  Object.assign(form, {
    name: '',
    shortName: '',
    type: '',
    bizModule: '', // 新增业务模块字段
    status: '',
    legalPerson: '',
    foundationDate: '',
    creditCode: '',
    registerAddress: '',
    businessAddress: '',
    mainBusiness: '',
    contactName: '',
    contactPhone: '',
    rating: 0,
    cooperationMode: '',
    signer: undefined,
    contractNo: '',
    contractStart: '',
    contractEnd: '',
    deposit: '',
    renewDate: '',
    accountName: '',
    settlementCycle: '',
    bankName: '',
    bankAccount: '',
    qualificationFile: [],
    invoiceType: '',
    invoiceName: '',
    taxId: '',
    orgCode: '',
    invoiceAddress: '',
    invoicePhone: '',
    invoiceBank: '',
    invoiceBankAccount: '',
    invoiceEmail: '',
    invoiceContact: '',
    invoiceQualificationFile: [],
    invoiceRemark: ''
  })
  if (mode.value === 'edit' && partnerData) {
    Object.assign(form, partnerData)
    form.signer = partnerData.owner ? Number(partnerData.owner) : undefined
    form.bizModule = partnerData.bizModule || ''
    // 资质文件回显（强类型防御）
    if (Array.isArray(partnerData.qualificationFile)) {
      form.qualificationFile = partnerData.qualificationFile.filter(f => typeof f === 'object' && f !== null)
    } else if (
      typeof partnerData.qualificationFile === 'string' &&
      partnerData.qualificationFile.trim() &&
      /^https?:\/\//.test(partnerData.qualificationFile)
    ) {
      form.qualificationFile = [{
        name: '资质文件',
        url: partnerData.qualificationFile,
        uid: Date.now() // 修正为 number 类型
      }]
    } else {
      form.qualificationFile = []
    }
    if (Array.isArray(partnerData.invoiceQualificationFile)) {
      form.invoiceQualificationFile = partnerData.invoiceQualificationFile.filter(f => typeof f === 'object' && f !== null)
    } else if (
      typeof partnerData.invoiceQualificationFile === 'string' &&
      partnerData.invoiceQualificationFile.trim() &&
      /^https?:\/\//.test(partnerData.invoiceQualificationFile)
    ) {
      form.invoiceQualificationFile = [{
        name: '开票资质文件',
        url: partnerData.invoiceQualificationFile,
        uid: Date.now() // 修正为 number 类型
      }]
    } else {
      form.invoiceQualificationFile = []
    }
  }
  drawerTitle.value = mode.value === 'edit' ? '编辑合作伙伴' : '新增合作伙伴'
  signerOptions.value = [] // 先清空，防止脏数据
  visible.value = true
  await Promise.all([
    loadSignerOptions(),
    loadOrgTypeOptions(),
    loadStatusOptions(),
    loadBizModuleOptions() // 新增业务模块选项加载
  ])
}

defineExpose({ open })
</script>

<style scoped lang="scss">
.partner-form {
  padding-right: 8px;
  max-height: 80vh;
  overflow-y: auto;
}
.form-section {
  background: #f7fafd;
  border-radius: 10px;
  padding: 18px 18px 8px 18px;
  margin-bottom: 20px;
}
.form-group-title {
  font-weight: bold;
  font-size: 16px;
  margin: 18px 0 8px 0;
}
.form-group-title-blue {
  color: #409EFF;
}
.form-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 24px;
  padding-bottom: 8px;
}
</style>
