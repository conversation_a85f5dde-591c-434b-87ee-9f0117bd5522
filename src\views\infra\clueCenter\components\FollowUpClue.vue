<template>
  <el-drawer
    v-model="visible"
    :title="drawerTitle"
    direction="rtl"
    size="500px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <!-- 线索备注 -->
    <div v-if="clueInfo.remark" class="clue-remark-card">
      <div class="remark-title">线索备注</div>
      <el-card shadow="never" class="remark-card">
        {{ clueInfo.remark }}
      </el-card>
    </div>

    <!-- 添加跟进记录 -->
    <div class="add-followup-section">
      <div class="section-title">添加跟进记录</div>
      <el-input
        v-model="followUpContent"
        type="textarea"
        :rows="3"
        maxlength="200"
        show-word-limit
        placeholder="在此输入跟进内容，例如：已电话沟通，客户已回复，客户意向良好。"
        style="margin-bottom: 8px"
      />
      <el-button type="primary" @click="addFollowUp" :disabled="!followUpContent.trim()"
        >添加跟进</el-button
      >
    </div>

    <!-- 跟进记录时间线 -->
    <div v-loading="loading" element-loading-text="加载中...">
      <el-timeline class="followup-timeline" v-if="followUpList.length > 0">
        <el-timeline-item
          v-for="(item, idx) in followUpList"
          :key="idx"
          :timestamp="item.createTimeFormatted"
          placement="top"
        >
          <div>
            <b v-if="item.creatorName || item.creator">{{ item.creatorName || item.creator }}</b>
            <span v-if="item.creatorName || item.creator">：</span>
            {{ item.followUpContent }}
          </div>
        </el-timeline-item>
      </el-timeline>
      <el-empty v-else-if="!loading" description="暂无跟进记录" :image-size="80" />
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineExpose } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import { ClueCenterApi, type FollowUpRecord } from '@/api/infra/clueCenter'

const visible = ref(false)
const loading = ref(false)
const clueInfo = reactive({
  leadId: '',
  customerName: '',
  remark: ''
})
const drawerTitle = computed(() =>
  clueInfo.customerName ? `"${clueInfo.customerName}"的跟进记录` : '线索跟进记录'
)

const followUpContent = ref('')
const followUpList = ref<FollowUpRecord[]>([])

// 加载跟进记录
async function loadFollowUpRecords() {
  if (!clueInfo.leadId) return

  try {
    loading.value = true
    const result = await ClueCenterApi.getFollowUpList({ leadId: clueInfo.leadId })
    followUpList.value = result || []
  } catch (error) {
    console.error('加载跟进记录失败:', error)
    ElMessage.error('加载跟进记录失败')
  } finally {
    loading.value = false
  }
}

async function open(clue: any) {
  visible.value = true
  // 线索列表传入的是 id 字段，需要映射为 leadId
  clueInfo.leadId = clue.leadId || ''
  clueInfo.customerName = clue.customerName || ''
  clueInfo.remark = clue.remark || ''

  // 加载跟进记录
  await loadFollowUpRecords()
}

function close() {
  visible.value = false
  // 清空数据
  clueInfo.leadId = ''
  clueInfo.customerName = ''
  clueInfo.remark = ''
  followUpContent.value = ''
  followUpList.value = []
}

async function addFollowUp() {
  if (!followUpContent.value.trim()) return
  if (!clueInfo.leadId) {
    ElMessage.error('线索ID不能为空')
    return
  }

  try {
    const loadingInstance = ElLoading.service({
      text: '添加中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    await ClueCenterApi.addFollowUp({
      leadId: clueInfo.leadId,
      followUpContent: followUpContent.value.trim()
    })

    // 添加成功后重新加载跟进记录
    await loadFollowUpRecords()
    followUpContent.value = ''
    ElMessage.success('添加成功')

    loadingInstance.close()
  } catch (error) {
    console.error('添加跟进记录失败:', error)
    ElMessage.error('添加跟进记录失败')
  }
}

defineExpose({ open, close })
</script>

<style scoped lang="scss">
.clue-remark-card {
  margin-bottom: 16px;
}
.remark-title {
  font-weight: bold;
  margin-bottom: 4px;
}
.remark-card {
  font-size: 14px;
  background: #f8fafd;
  border-radius: 6px;
  padding: 12px 16px;
}
.add-followup-section {
  margin-bottom: 16px;
}
.section-title {
  font-weight: bold;
  margin-bottom: 4px;
}
.followup-timeline {
  margin-top: 8px;
}
</style>
