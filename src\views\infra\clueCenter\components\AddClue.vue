<template>
  <el-drawer
    v-model="visible"
    :title="drawerTitle"
    direction="rtl"
    size="500px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="110px"
      label-position="top"
      style="margin-right: 16px"
    >
      <el-form-item label="客户姓名" prop="customerName">
        <el-input
          v-model="form.customerName"
          placeholder="客户的姓名"
          maxlength="20"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="联系电话" prop="customerPhone">
        <el-input v-model="form.customerPhone" placeholder="必填，11位手机号" maxlength="11" />
      </el-form-item>
      <el-form-item label="线索来源" prop="leadSource">
        <el-select v-model="form.leadSource" placeholder="请选择线索来源">
          <el-option
            v-for="item in leadSourceOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="业务模块" prop="businessModuleName">
        <el-select v-model="form.businessModuleName" placeholder="请选择业务模块">
          <el-option
            v-for="item in businessModuleOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="线索状态" prop="leadStatus">
        <el-select v-model="form.leadStatus" placeholder="请选择线索状态">
          <el-option
            v-for="item in leadStatusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="当前跟进人" prop="currentOwner">
        <el-select v-model="form.currentOwner" placeholder="请选择跟进人" clearable>
          <el-option label="未分配" value="0" />
          <el-option
            v-for="user in userOptions"
            :key="user.id"
            :label="user.nickname || user.username"
            :value="user.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="备注信息" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="3"
          maxlength="500"
          show-word-limit
          placeholder="请输入对本条线索的详细说明，如客户需求、沟通记录、特殊要求等"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div style="text-align: right">
        <el-button @click="onCancel">取消</el-button>
        <el-button type="primary" @click="onSubmit">保存</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineExpose, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { ClueCenterApi } from '@/api/infra/clueCenter/index'
import { getSimpleUserList } from '@/api/system/user'

// 定义事件
const emit = defineEmits(['success'])

// 控制抽屉显示
const visible = ref(false)
// 抽屉标题
const isEdit = ref(false)
const drawerTitle = computed(() => (isEdit.value ? '编辑线索' : '新建线索'))

// 表单数据
const form = reactive({
  id: '', // 编辑时需要的线索ID
  customerName: '',
  customerPhone: '',
  leadSource: '',
  businessModuleName: '',
  leadStatus: '',
  currentOwner: '',
  currentOwnerName: '',
  createMethod: '', // 创建方式
  remark: ''
})

// 表单校验规则
const rules = {
  customerName: [{ required: true, message: '请输入客户姓名', trigger: 'blur' }],
  customerPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1\d{10}$/, message: '请输入11位手机号', trigger: 'blur' }
  ],
  leadSource: [{ required: true, message: '请选择线索来源', trigger: 'change' }],
  businessModuleName: [{ required: true, message: '请选择业务模块', trigger: 'change' }],
  leadStatus: [{ required: true, message: '请选择线索状态', trigger: 'change' }],
  currentOwner: [{ required: true, message: '请选择当前跟进人', trigger: 'change' }]
}

// 下拉选项（本地静态）
const leadSourceOptions = ref([
  { label: '官网注册', value: 1 },
  { label: '市场活动', value: 2 },
  { label: '公众号文章', value: 3 },
  { label: '视频号', value: 4 },
  { label: '抖音', value: 5 },
  { label: '客户推荐', value: 6 },
  { label: '电话营销', value: 7 },
  { label: '社交媒体', value: 8 },
  { label: '展会', value: 9 },
  { label: '其他', value: 99 }
])
const businessModuleOptions = ref([
  { label: '家政业务', value: 2 },
  { label: '高校业务', value: 1 },
  { label: '培训业务', value: 3 },
  { label: '认证业务', value: 4 }
])
const leadStatusOptions = ref([
  { label: '未处理', value: 1 },
  { label: '跟进中', value: 2 },
  { label: '已转化', value: 3 },
  { label: '无意向', value: 4 }
])
const userOptions = ref<any[]>([])

const formRef = ref()

// 获取用户列表
const fetchUserOptions = async () => {
  try {
    const userRes = await getSimpleUserList()
    userOptions.value = userRes || []
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  }
}

// 对外暴露方法，供父组件控制显示
function open(data: any = null) {
  visible.value = true
  if (data) {
    isEdit.value = true
    // 编辑时映射数据，注意字段名的转换
    Object.assign(form, {
      id: data.id || '',
      customerName: data.customerName || '',
      customerPhone: data.customerPhone || '',
      leadSource: data.leadSource || '',
      businessModuleName: data.businessModule || '', // 注意字段映射
      leadStatus: data.leadStatus || '',
      currentOwner:
        data.currentOwner && data.currentOwner !== '0' ? parseInt(data.currentOwner) : '0',
      currentOwnerName:
        data.currentOwner && data.currentOwner !== '0' ? data.currentOwnerName : '未分配',
      createMethod: data.createMethod || '', // 创建方式
      remark: data.remark || ''
    })
  } else {
    isEdit.value = false
    Object.assign(form, {
      id: '',
      customerName: '',
      customerPhone: '',
      leadSource: '',
      businessModuleName: '',
      leadStatus: '',
      currentOwner: '0',
      currentOwnerName: '未分配',
      createMethod: '手动创建', // 新增时默认为手动创建
      remark: ''
    })
  }
}
function close() {
  visible.value = false
}

// 取消
function onCancel() {
  close()
}
// 保存
function onSubmit() {
  formRef.value.validate(async (valid: boolean) => {
    if (!valid) return
    try {
      // 获取选中用户的姓名
      const selectedUser = userOptions.value.find((user) => user.id === form.currentOwner)
      const currentOwnerName = selectedUser
        ? selectedUser.nickname || selectedUser.username
        : '未分配'

      const params = {
        customerName: form.customerName,
        customerPhone: form.customerPhone,
        leadSource: form.leadSource,
        businessModule: form.businessModuleName, // 映射
        leadStatus: form.leadStatus,
        currentOwner: form.currentOwner ? form.currentOwner.toString() : '',
        currentOwnerName: currentOwnerName, // 当前跟进人姓名
        createMethod: form.createMethod, // 创建方式
        remark: form.remark
      }

      if (isEdit.value) {
        // 编辑模式：调用更新接口
        const updateParams = {
          id: form.id,
          ...params
        }
        await ClueCenterApi.updateClue(updateParams)
        ElMessage.success('编辑成功')
        emit('success') // 通知父组件刷新列表
      } else {
        // 新增模式：调用新增接口
        await ClueCenterApi.addClue(params)
        ElMessage.success('新建成功')
        emit('success') // 通知父组件刷新列表
      }
      close()
    } catch (e) {
      console.error('保存失败:', e)
      ElMessage.error(isEdit.value ? '编辑失败' : '新建失败')
    }
  })
}

// 组件挂载时获取用户列表
onMounted(() => {
  fetchUserOptions()
})

defineExpose({ open, close })
</script>

<style scoped lang="scss">
.el-drawer__body {
  padding-bottom: 0;
}
</style>
