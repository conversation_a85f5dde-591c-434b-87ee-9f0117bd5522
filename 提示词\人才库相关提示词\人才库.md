---
description: 
globs: 
alwaysApply: false
---


搜索区用 <ContentWrap> 包裹，label+输入框风格，按钮加图标。
表格用 <ContentWrap> 包裹，表头居中，内容溢出省略，操作按钮风格一致。
分页用 <Pagination> 组件，紧贴表格下方。
输入框、下拉框宽度统一（如 240px）。


搜索区域：
使用 <ContentWrap> 包裹，背景有卡片感。
搜索项有 label，输入框宽度统一（如 !w-240px）。
搜索按钮有图标，按钮紧凑，常用"搜索""重置"。
右侧有"新增"等操作按钮，风格为 type="primary" plain。
搜索表单底部无分割线，整体紧凑。


数据列表：
使用 <ContentWrap> 包裹表格。
表格有多选框（type="selection"），表头居中，内容溢出省略。
操作列按钮为 link 类型，带图标，常用"修改""更多"下拉。
分页控件单独用 <Pagination> 组件，紧贴表格下方。



现在实现人才库页面的功能
对应的页面新增到 \src\views\apachecore\talentpool

1. 搜索条件：
	文本框：搜索姓名/手机/身份证号码  模糊匹配
	用户来源：下拉框展示（固定值：全部、高校实践小程序、家政服务员注册、技能培训报名） 单选
	用户标签：文本搜索框，可以输入标签名字匹配数据源
	用户状态：（固定值：全部、正常、待合并、已禁用）
 查询条件后是 搜索、重置按钮
2. 查询按钮功能说明：
	搜索：点击搜索请求后台API查询接口，将返回的数据绑定到列表分页展示
	重置：点击重置则清除所有的搜索条件
3. 列表展示字段：
	用户信息、关键标签、档案完整度、状态	操作

	操作栏按钮包括：
		查看画像：
		编辑:
		停用:状态是正常时 展示此按钮
		发起合并:状态是待合并时 展示此按钮
4. 底部展示分页控件

具体界面设计参考附件截图


请参考附件截图，搜索区域的上方有一条分割线，
分割线上面的左侧是文本：人才洞察中心(文本加粗展示)，
右侧是新建人才：文本前面是一个添加的图标，鼠标移上去会有小手标识，点击按钮弹窗展示新建人才档案的界面


现在调整实践与项目界面：
 1. 实践名称：改成下拉框，单选（固定值：实践A、实践B、实践C）
 2. 实习公司：改成下拉框，单选（固定值：公司A、公司B、公司C）
 3. 项目名称：改成下拉框，单选（固定值：项目A、项目B、项目C）

现在实现人才库列表操作栏的查看画像功能，具体要求如下：
1.点击查看画像，界面从右侧滑出，大概占整体界面的三分之一
2.界面设计参考截图的 人才画像详情 界面，以列表"张三"数据为例
3.请注意：生命周期时间轴需要按照日期倒叙展示
4.暂无API接口，使用静态数据模拟实现


上述实现不对，需要删除index.vue 文件中关于"人才画像详情"抽屉（el-drawer）的所有相关代码，操作栏的查看画像"按钮保留不能删除，人才画像详情的界面只会在 TalentDetail.vue 文件中实现。点击操作栏的查看画像"按钮右侧抽屉滑出 人才画像详情界面



 根据附件图片截图完善人才画像详情界面的设计：
 1.底部新增 详细档案
 2. 样式调整：顶部的间距缩小一点



 调整一下人才画像详情界面的样式：
 1.人才画像详情下面新增一条浅色的分割线
 2.人才标签云下面新增一条浅色的分割线
 3. 生命周期时间轴下面新增一条浅色的分割线
 4. 教育背景的数据信息 每一条数据的下方增加一条浅色的分割线
 5.缩小详细档案和上方的间距
 6.详细档案的模块去掉边框线，左右间距缩小到5px


参考附件截图依次实现详细档案的各个tab功能：实践与项目、技能与培训、认证与资质、求职与工作、用户评价（详细档案的评分 五角星颜色改成 #f39c12）


现在请调整详细档案的导出简历功能：
 1. 导出简历按钮文本前面增加一个导出pdf的图标
 2. 点击导出简历自动下载当前界面展示的人对应的信息，保留当前界面的所有样式以及排版导出到pdf文件，且在pdf里面展示的详细档案的tab保留可切换功能,
 无法实现切换功能则依次平铺展示


 目前的导出功能存在以下问题：
1. 导出的文件名称不对，请按照当前界面的人才姓名命名文件，如：张三个人简历.pdf
2. 目前的导出pdf文件内容只包含了 详细档案部分，实际需要导出整个界面展示的内容 包括人才画像资料、人才标签云、生命周期时间轴以及详细档案

导出简历依然存在问题：导出的详细档案部分没有平铺展示，只展示了教育背景的内容，请调整


导出的问题又出现了 目前导出的文件只有详细档案部分，请调整，实际需要导出整个界面展示的内容 包括人才画像资料、人才标签云、生命周期时间轴以及详细档案，详细档案平铺展示，注意pdf文件的样式，左侧需要留部分间距
 

 --- 接口实现：
 附件中index.vue是人才库的列表页，现在请按照人才库接口对接.md文档的内容实现人才库搜索功能，根据查询区域的值获取数据库的人才库数据并实现分页加载。


 ------0709----------

 现在请调整人才库新建和编辑界面src\views\apachecore\talentpool\components\AddTalent.vue
 1. 教育背景：需要支持添加多项，所以在下方添加一条分割线，分割线的下方增加一个按钮"+添加一项"


 现在来实现数据列表操作栏的停用功能，停用状态下可以再次启用。请求接口地址/apachecore/talentpool/change-status .具体接口文档参考附件人才库接口对接.md 



 @基础数据字典接口文档.md @AddTalent.vue 人才来源的下拉框数据源绑定接口参考附件基础数据字典接口, 下拉数据的绑定不进行分页，所以pageSize默认传递20 接口地址：/system/dict-data/page?dictType=talent_sources&label=&pageNo=1&pageSize=10&status=0