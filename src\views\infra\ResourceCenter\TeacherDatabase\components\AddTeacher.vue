<!--
  页面名称：新增师资
  功能描述：新增师资，支持表单校验、抽屉展示、文件上传
-->
<template>
  <el-drawer
    v-model="visible"
    :title="editData ? '编辑师资' : '新增师资'"
    size="700px"
    direction="rtl"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="onCancel"
  >
    <el-form
      :model="form"
      :rules="rules"
      ref="formRef"
      label-width="100px"
      class="add-teacher-form"
    >
      <div class="section-title">核心信息</div>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="讲师姓名" prop="name" required>
            <el-input v-model="form.name" placeholder="必填" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="讲师类型" prop="type">
            <el-select v-model="form.type" placeholder="请选择讲师类型">
              <el-option
                v-for="item in teacherTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合作状态" prop="status">
            <el-select v-model="form.status" placeholder="请选择合作状态">
              <el-option
                v-for="item in cooperativeStatusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="业务模块" prop="biz" required>
            <el-select v-model="form.biz" placeholder="请选择业务模块">
              <el-option
                v-for="item in businessModuleOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="擅长领域" prop="field">
            <el-input v-model="form.field" placeholder="多个领域用逗号分隔" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关联机构" prop="org">
            <el-select v-model="form.org" placeholder="请选择关联机构" clearable filterable>
              <el-option label="无关联机构" value="" />
              <el-option
                v-for="item in partnerOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
                <span>{{ item.label }}</span>
                <span v-if="item.shortName" style="color: #909399; margin-left: 8px">
                  ({{ item.shortName }})
                </span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="phone">
            <el-input v-model="form.phone" placeholder="请输入手机号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电子签约状态" prop="signStatus" required>
            <el-select v-model="form.signStatus" placeholder="请选择电子签约状态">
              <el-option
                v-for="item in elecSignatureStatusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="讲师头像" prop="avatar" required>
            <el-upload
              class="avatar-uploader"
              :show-file-list="false"
              :auto-upload="false"
              :on-change="handleAvatarChange"
              accept="image/*"
            >
              <div v-if="form.avatar.length > 0 && form.avatar[0].url" class="avatar-preview">
                <img :src="form.avatar[0].url" class="avatar-image" />
                <div class="avatar-mask">
                  <el-icon><i class="el-icon-edit"></i></el-icon>
                </div>
              </div>
              <div v-else class="avatar-upload-trigger">
                <el-icon><i class="el-icon-plus"></i></el-icon>
                <div class="upload-text">点击上传头像</div>
              </div>
            </el-upload>
            <div class="upload-tip">支持 JPG、PNG 格式，建议尺寸 200x200 像素</div>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="section-title">讲师简介</div>
      <el-form-item label="讲师简介和资历" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="4"
          placeholder="详细介绍讲师的背景、资历、授课风格等..."
        />
      </el-form-item>
      <div class="section-title">资质文件</div>
      <el-button type="primary" @click="addCertFile" style="margin-bottom: 16px"
        >+ 添加资质文件</el-button
      >
      <div v-for="(item, idx) in form.certFiles" :key="idx" class="cert-file-card">
        <div class="cert-file-header">
          <span>资质文件 #{{ idx + 1 }}</span>
          <el-button
            type="danger"
            icon="el-icon-delete"
            size="small"
            @click="removeCertFile(idx)"
            v-if="form.certFiles.length > 1"
            >删除</el-button
          >
        </div>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item
              :label="'资质类型'"
              :prop="`certFiles.${idx}.certType`"
              :rules="[{ required: true, message: '请选择资质类型', trigger: 'change' }]"
            >
              <el-select v-model="item.certType" placeholder="请选择资质类型">
                <el-option
                  v-for="item in qualificationTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="'资质名称'"
              :prop="`certFiles.${idx}.certName`"
              :rules="[{ required: true, message: '请输入资质名称', trigger: 'blur' }]"
            >
              <el-input
                v-model="item.certName"
                placeholder="如：高级母婴护理师证、金牌月嫂证书、深度保洁技能证书等"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="'有效期开始'" :prop="`certFiles.${idx}.validStart`">
              <el-date-picker
                v-model="item.validStart"
                type="date"
                placeholder="年/月/日"
                format="YYYY/MM/DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="'有效期结束'" :prop="`certFiles.${idx}.validEnd`">
              <el-date-picker
                v-model="item.validEnd"
                type="date"
                placeholder="年/月/日"
                format="YYYY/MM/DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              :label="'资质证书文件'"
              :prop="`certFiles.${idx}.files`"
              :rules="[{ required: true, message: '请上传资质证书文件', trigger: 'change' }]"
            >
              <el-upload
                class="upload-demo"
                drag
                :show-file-list="true"
                :http-request="(option) => handleCertUploadRequest(idx, option)"
                :on-remove="(file) => handleCertRemove(idx, file)"
                :on-change="(file, fileList) => console.log('文件变化:', file, fileList)"
                multiple
                :file-list="item.files"
                :accept="'.pdf,.doc,.docx,.jpg,.jpeg,.png'"
              >
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                <template #tip>
                  <div class="el-upload__tip"
                    >支持格式：PDF, Word, JPG, PNG<br />可以上传多个文件，包括：技能等级证书、从业资格证书、专业技能证书、职业资格证书等</div
                  >
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div class="section-title">合同管理</div>
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="合同类型" prop="contractType">
            <el-radio-group v-model="form.contractType">
              <el-radio label="电子合同">电子合同</el-radio>
              <el-radio label="纸质合同">纸质合同</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="form.contractType === '电子合同'">
          <el-form-item label="合同模板" prop="contractTemplate" required>
            <el-select v-model="form.contractTemplate" placeholder="请选择合同模板">
              <el-option
                v-for="item in contractTemplateOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <template v-if="form.contractType === '纸质合同'">
          <el-col :span="24">
            <el-form-item label="合同附件" prop="contractFile">
              <el-upload
                class="upload-demo"
                :show-file-list="true"
                :http-request="handleContractUploadRequest"
                :on-remove="handleContractRemove"
                :limit="1"
                :accept="'.pdf,.doc,.docx,.jpg,.jpeg,.png'"
              >
                <el-button>选择文件</el-button>
                <template #tip>
                  <div class="el-upload__tip">未选择任何文件</div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="合同编号" prop="contractNo">
              <el-input v-model="form.contractNo" placeholder="请输入合同编号" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="合同周期" prop="contractPeriod" required>
              <el-date-picker
                v-model="form.contractPeriod"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY/MM/DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="合同名称" prop="contractName">
              <el-input v-model="form.contractName" placeholder="请输入合同名称" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="签署日期" prop="signDate">
              <el-date-picker
                v-model="form.signDate"
                type="date"
                placeholder="请选择签署日期"
                format="YYYY/MM/DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="合同金额" prop="contractAmount">
              <el-input v-model="form.contractAmount" placeholder="请输入合同金额" />
            </el-form-item>
          </el-col>
        </template>
      </el-row>
      <div class="dialog-footer">
        <el-button @click="onCancel">取消</el-button>
        <el-button type="primary" @click="onSubmit" :loading="submitLoading">保存</el-button>
      </div>
    </el-form>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { getDictDataPage } from '@/api/system/dict/dict.data'
import { createTeacher, updateTeacher } from '@/api/infra/teacher'
import request from '@/config/axios'
import { formatDate as formatDateUtil } from '@/utils/formatTime'

// 格式化日期为 yyyy-MM-dd 格式的包装函数
function formatDate(dateValue: any): string {
  if (!dateValue) return ''

  try {
    let date: Date

    // 如果是时间戳（数字）
    if (typeof dateValue === 'number') {
      date = new Date(dateValue)
    }
    // 如果是日期字符串
    else if (typeof dateValue === 'string') {
      date = new Date(dateValue)
    }
    // 如果是Date对象
    else if (dateValue instanceof Date) {
      date = dateValue
    } else {
      return ''
    }

    return formatDateUtil(date, 'YYYY-MM-DD')
  } catch (error) {
    console.error('日期格式化失败:', error)
    return ''
  }
}

const emit = defineEmits(['close'])
const visible = defineModel<boolean>('visible', { default: false })
const editData = defineModel<any>('editData', { default: null })
const formRef = ref()
const submitLoading = ref(false)

// 字典数据选项
const teacherTypeOptions = ref<any[]>([])
const cooperativeStatusOptions = ref<any[]>([])
const qualificationTypeOptions = ref<any[]>([])
const businessModuleOptions = ref<any[]>([])
const elecSignatureStatusOptions = ref<any[]>([])
const contractTemplateOptions = ref<any[]>([])
// 合作伙伴数据选项
const partnerOptions = ref<any[]>([])

const form = reactive({
  name: '',
  type: '',
  status: '',
  biz: '',
  field: '',
  org: '',
  phone: '',
  signStatus: '',
  avatar: [] as any[],
  description: '',
  certFiles: [
    { certId: null, certType: '', certName: '', validStart: '', validEnd: '', files: [] as any[] }
  ] as any[],
  contractType: '电子合同',
  contractTemplate: '',
  contractFile: [] as any[],
  contractNo: '',
  contractPeriod: [] as any[],
  contractName: '',
  signDate: '',
  contractAmount: ''
})

const rules = {
  name: [{ required: true, message: '请输入讲师姓名', trigger: 'blur' }],
  type: [{ required: true, message: '请选择讲师类型', trigger: 'change' }],
  status: [{ required: true, message: '请选择合作状态', trigger: 'change' }],
  biz: [{ required: true, message: '请选择业务模块', trigger: 'change' }],
  signStatus: [{ required: true, message: '请选择电子签约状态', trigger: 'change' }],
  avatar: [
    {
      required: true,
      message: '请上传讲师头像',
      trigger: 'change',
      validator: (_rule: any, value: any, callback: any) => {
        if (!value || value.length === 0 || !value[0].url) {
          callback(new Error('请上传讲师头像'))
        } else {
          callback()
        }
      }
    }
  ],
  phone: [{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }],
  description: [{ required: true, message: '请输入讲师简介', trigger: 'blur' }],
  contractType: [{ required: true, message: '请选择合同类型', trigger: 'change' }],
  contractTemplate: [
    {
      required: true,
      message: '请选择合同模板',
      trigger: 'change',
      validator: (_rule: any, value: any, callback: any) => {
        if (form.contractType === '电子合同' && !value) {
          callback(new Error('请选择合同模板'))
        } else {
          callback()
        }
      }
    }
  ],
  contractFile: [
    {
      required: true,
      message: '请上传合同附件',
      trigger: 'change',
      validator: (_rule: any, value: any, callback: any) => {
        if (form.contractType === '纸质合同' && (!value || value.length === 0)) {
          callback(new Error('请上传合同附件'))
        } else {
          callback()
        }
      }
    }
  ],
  contractNo: [{ required: false }],
  contractPeriod: [
    {
      required: true,
      message: '请选择合同周期',
      trigger: 'change',
      validator: (_rule: any, value: any, callback: any) => {
        if (form.contractType === '纸质合同' && (!value || value.length !== 2)) {
          callback(new Error('请选择合同周期'))
        } else {
          callback()
        }
      }
    }
  ],
  contractName: [{ required: false }],
  signDate: [{ required: false }],
  contractAmount: [{ required: false }]
}

// 获取字典数据
const loadDictData = async () => {
  try {
    const [
      teacherTypeRes,
      cooperativeStatusRes,
      qualificationTypeRes,
      businessModuleRes,
      elecSignatureStatusRes,
      contractTemplateRes
    ] = await Promise.all([
      getDictDataPage({ dictType: 'teacher_type', pageNo: 1, pageSize: 100 }),
      getDictDataPage({ dictType: 'cooperative_status', pageNo: 1, pageSize: 100 }),
      getDictDataPage({ dictType: 'qualification_type', pageNo: 1, pageSize: 100 }),
      getDictDataPage({ dictType: 'business_module', pageNo: 1, pageSize: 100 }),
      getDictDataPage({ dictType: 'elec_signature_status', pageNo: 1, pageSize: 100 }),
      getDictDataPage({ dictType: 'contract_template', pageNo: 1, pageSize: 100 })
    ])

    teacherTypeOptions.value = (teacherTypeRes?.list || []).map((item) => ({
      label: item.label,
      value: item.value
    }))
    cooperativeStatusOptions.value = (cooperativeStatusRes?.list || []).map((item) => ({
      label: item.label,
      value: item.value
    }))
    qualificationTypeOptions.value = (qualificationTypeRes?.list || []).map((item) => ({
      label: item.label,
      value: item.value
    }))
    businessModuleOptions.value = (businessModuleRes?.list || []).map((item) => ({
      label: item.label,
      value: item.value
    }))
    elecSignatureStatusOptions.value = (elecSignatureStatusRes?.list || []).map((item) => ({
      label: item.label,
      value: item.value
    }))
    contractTemplateOptions.value = (contractTemplateRes?.list || []).map((item) => ({
      label: item.label,
      value: item.value
    }))
  } catch (error) {
    console.error('获取字典数据失败:', error)
  }
}

// 获取合作伙伴数据
const loadPartnerData = async () => {
  try {
    const partnerRes = await request.get({ url: '/publicbiz/partner/list/active' })

    // 根据实际返回的数据结构处理
    let partnerList = []
    if (partnerRes?.list) {
      partnerList = partnerRes.list
    } else if (partnerRes?.data?.list) {
      partnerList = partnerRes.data.list
    } else if (Array.isArray(partnerRes)) {
      partnerList = partnerRes
    } else if (partnerRes?.data && Array.isArray(partnerRes.data)) {
      partnerList = partnerRes.data
    }

    if (partnerList && partnerList.length > 0) {
      partnerOptions.value = partnerList.map((item: any) => ({
        label: item.name,
        value: item.name,
        shortName: item.shortName,
        type: item.type,
        biz: item.biz,
        status: item.status
      }))
    } else {
      partnerOptions.value = []
    }
  } catch (error) {
    console.error('获取合作伙伴数据失败:', error)
    partnerOptions.value = []
  }
}

// 头像上传处理
async function handleAvatarChange(file: any) {
  try {
    const formData = new FormData()
    formData.append('file', file.raw)

    const res = await request.post({
      url: '/infra/file/upload',
      data: formData,
      headers: { 'Content-Type': 'multipart/form-data' }
    })

    // 兼容res为字符串（图片url）
    let imgUrl = ''
    if (typeof res === 'string') {
      imgUrl = res
    } else if (res && res.data && typeof res.data === 'string') {
      imgUrl = res.data
    } else if (res && res.data && res.data.data) {
      imgUrl = res.data.data
    }

    if (imgUrl) {
      form.avatar = [
        {
          ...file,
          url: imgUrl,
          name: file.name
        }
      ]
      ElMessage.success('头像上传成功')
    } else {
      ElMessage.error('头像上传失败')
    }
  } catch (error) {
    console.error('头像上传失败:', error)
    ElMessage.error('头像上传失败')
  }
}

function addCertFile() {
  form.certFiles.push({
    certId: null, // 新增的资质文件ID为null
    certType: '',
    certName: '',
    validStart: '',
    validEnd: '',
    files: [] as any[]
  })
}

function removeCertFile(idx: number) {
  if (form.certFiles.length > 1) form.certFiles.splice(idx, 1)
}

// 资质文件上传处理
async function handleCertUploadRequest(idx: number, option: any) {
  console.log('handleCertUploadRequest 被调用', idx, option)
  const formData = new FormData()
  formData.append('file', option.file)

  try {
    console.log('开始上传文件到 /infra/file/upload')
    const res = await request.upload({
      url: '/infra/file/upload',
      data: formData
    })

    console.log('上传响应:', res)

    // 兼容res为字符串（文件url）
    let fileUrl = ''
    if (typeof res === 'string') {
      fileUrl = res
    } else if (res && res.data && typeof res.data === 'string') {
      fileUrl = res.data
    } else if (res && res.data && res.data.data) {
      fileUrl = res.data.data
    }

    console.log('提取的文件URL:', fileUrl)

    if (fileUrl) {
      const uploadedFile = {
        name: option.file.name,
        url: fileUrl,
        status: 'done',
        uid: option.file.uid || Date.now().toString()
      }

      // 添加到对应资质文件的文件列表中
      if (!form.certFiles[idx].files) {
        form.certFiles[idx].files = []
      }
      form.certFiles[idx].files.push(uploadedFile)

      console.log('文件上传成功，添加到列表:', uploadedFile)
      option.onSuccess(res, option.file)
      ElMessage.success('文件上传成功')
    } else {
      console.error('文件URL提取失败')
      option.onError(new Error('上传失败'))
      ElMessage.error('文件上传失败')
    }
  } catch (error) {
    console.error('文件上传失败:', error)
    option.onError(error)
    ElMessage.error('文件上传失败')
  }
}

// 资质文件移除处理
function handleCertRemove(idx: number, file: any) {
  const fileIndex = form.certFiles[idx].files.findIndex((f: any) => f.uid === file.uid)
  if (fileIndex > -1) {
    form.certFiles[idx].files.splice(fileIndex, 1)
  }
}

// 合同文件上传处理
async function handleContractUploadRequest(option: any) {
  const formData = new FormData()
  formData.append('file', option.file)

  try {
    const res = await request.upload({
      url: '/infra/file/upload',
      data: formData
    })

    // 兼容res为字符串（文件url）
    let fileUrl = ''
    if (typeof res === 'string') {
      fileUrl = res
    } else if (res && res.data && typeof res.data === 'string') {
      fileUrl = res.data
    } else if (res && res.data && res.data.data) {
      fileUrl = res.data.data
    }

    if (fileUrl) {
      const uploadedFile = {
        name: option.file.name,
        url: fileUrl,
        status: 'done',
        uid: option.file.uid || Date.now().toString()
      }

      form.contractFile = [uploadedFile]

      option.onSuccess(res, option.file)
      ElMessage.success('合同文件上传成功')
    } else {
      option.onError(new Error('上传失败'))
      ElMessage.error('合同文件上传失败')
    }
  } catch (error) {
    console.error('合同文件上传失败:', error)
    option.onError(error)
    ElMessage.error('合同文件上传失败')
  }
}

// 合同文件移除处理
function handleContractRemove(file: any) {
  form.contractFile = []
}

async function onSubmit() {
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      submitLoading.value = true
      try {
        // 判断是新增还是编辑
        const isEdit = editData.value && editData.value.id

        // 构建提交数据
        const submitData: any = {
          name: form.name,
          type: form.type,
          status: form.status,
          biz: form.biz,
          field: form.field,
          org: form.org,
          phone: form.phone,
          signStatus: form.signStatus,
          avatar: form.avatar.length > 0 ? form.avatar[0].url : '',
          description: form.description,
          contractType: form.contractType,
          contractTemplate: form.contractTemplate,
          contractNo: form.contractNo,
          contractName: form.contractName,
          contractPeriodStart: form.contractPeriod.length > 0 ? form.contractPeriod[0] : null,
          contractPeriodEnd: form.contractPeriod.length > 1 ? form.contractPeriod[1] : null,
          contractAmount: form.contractAmount ? parseFloat(form.contractAmount) : 0,
          contractFileName: form.contractFile.length > 0 ? form.contractFile[0].name : '',
          contractFileUrl: form.contractFile.length > 0 ? form.contractFile[0].url : '',
          signDate: form.signDate || null,
          certFiles: form.certFiles.map((item) => ({
            id: item.certId, // 传递资质文件ID，null表示新增，有值表示更新
            teacherId: isEdit ? editData.value.id : 0, // 编辑时使用讲师ID，新增时为0
            certType: item.certType,
            certName: item.certName,
            fileName: item.files.length > 0 ? item.files[0].name : '',
            fileUrl: item.files.length > 0 ? item.files[0].url : '',
            validStartDate: item.validStart ? new Date(item.validStart).getTime() : null,
            validEndDate: item.validEnd ? new Date(item.validEnd).getTime() : null
          }))
        }

        if (isEdit) {
          submitData.id = editData.value.id
        }

        const res = isEdit ? await updateTeacher(submitData) : await createTeacher(submitData)
        if (res || res.code === 0) {
          // 使用全局消息提示
          ElMessage.success(isEdit ? '编辑讲师成功' : '新增讲师成功')
          emit('close', true)
          visible.value = false
          editData.value = null
        } else {
          ElMessage.error(res?.msg || (isEdit ? '编辑讲师失败' : '新增讲师失败'))
        }
      } catch (error) {
        console.error('新增讲师失败:', error)
        ElMessage.error('新增讲师失败')
      } finally {
        submitLoading.value = false
      }
    }
  })
}

function onCancel() {
  visible.value = false
  editData.value = null
  emit('close', false)
}

// 监听编辑数据变化，反绑定到表单
watch(
  editData,
  (newData) => {
    if (newData) {
      // 反绑定数据到表单
      form.name = newData.name || ''
      form.type = newData.type || ''
      form.status = newData.status || ''
      form.biz = newData.biz || ''
      form.field = newData.field || ''
      form.org = newData.org || ''
      form.phone = newData.phone || ''
      form.signStatus = newData.signStatus || ''
      form.description = newData.description || ''
      form.contractType = newData.contractType || '电子合同'
      form.contractTemplate = newData.contractTemplate || ''
      form.contractNo = newData.contractNo || ''
      form.contractName = newData.contractName || ''
      form.contractAmount = newData.contractAmount ? newData.contractAmount.toString() : ''
      form.signDate = formatDate(newData.signDate) || ''

      // 处理头像
      if (newData.avatar) {
        form.avatar = [
          {
            name: 'avatar',
            url: newData.avatar,
            status: 'done',
            uid: Date.now().toString()
          }
        ]
      } else {
        form.avatar = []
      }

      // 处理合同文件
      if (newData.contractFileUrl) {
        form.contractFile = [
          {
            name: newData.contractFileName || 'contract',
            url: newData.contractFileUrl,
            status: 'done',
            uid: Date.now().toString()
          }
        ]
      } else {
        form.contractFile = []
      }

      // 处理合同周期
      if (newData.contractPeriodStart && newData.contractPeriodEnd) {
        form.contractPeriod = [
          formatDate(newData.contractPeriodStart),
          formatDate(newData.contractPeriodEnd)
        ]
      } else {
        form.contractPeriod = []
      }

      // 处理资质文件
      if (newData.certFiles && newData.certFiles.length > 0) {
        form.certFiles = newData.certFiles.map((cert: any) => ({
          certId: cert.id || null, // 保存资质文件ID，用于区分新增还是更新
          certType: cert.certType || '',
          certName: cert.certName || '',
          validStart: formatDate(cert.validStartDate) || '',
          validEnd: formatDate(cert.validEndDate) || '',
          files: cert.fileUrl
            ? ([
                {
                  name: cert.fileName || 'cert',
                  url: cert.fileUrl,
                  status: 'done',
                  uid: Date.now().toString()
                }
              ] as any[])
            : []
        }))
      } else {
        form.certFiles = [
          { certId: null, certType: '', certName: '', validStart: '', validEnd: '', files: [] }
        ]
      }
    } else {
      // 重置表单
      resetForm()
    }
  },
  { immediate: true }
)

// 重置表单
function resetForm() {
  form.name = ''
  form.type = ''
  form.status = ''
  form.biz = ''
  form.field = ''
  form.org = ''
  form.phone = ''
  form.signStatus = ''
  form.avatar = []
  form.description = ''
  form.certFiles = [
    { certId: null, certType: '', certName: '', validStart: '', validEnd: '', files: [] }
  ]
  form.contractType = '电子合同'
  form.contractTemplate = ''
  form.contractFile = []
  form.contractNo = ''
  form.contractPeriod = []
  form.contractName = ''
  form.signDate = ''
  form.contractAmount = ''
}

// 组件挂载时加载字典数据
onMounted(() => {
  loadDictData()
  loadPartnerData()
})
</script>

<style scoped lang="scss">
.add-teacher-form {
  padding-bottom: 0;
}
.section-title {
  font-weight: bold;
  font-size: 16px;
  margin: 16px 0 8px 0;
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  gap: 8px;
}
.upload-demo {
  display: block;
}
.cert-file-card {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 18px;
  background: #fff;
}
.cert-file-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  margin-bottom: 12px;
}

.avatar-uploader {
  display: block;
}

.avatar-preview {
  position: relative;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
  color: white;
  font-size: 20px;
}

.avatar-preview:hover .avatar-mask {
  opacity: 1;
}

.avatar-upload-trigger {
  width: 100px;
  height: 100px;
  border: 1px dashed #d9d9d9;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.3s;
}

.avatar-upload-trigger:hover {
  border-color: #409eff;
}

.avatar-upload-trigger .el-icon {
  font-size: 28px;
  color: #8c939d;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 12px;
  color: #8c939d;
}

.upload-tip {
  font-size: 12px;
  color: #8c939d;
  margin-top: 8px;
}
</style>
