package cn.bztmaster.cnt.module.publicbiz.convert.question;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.QuestionOptionRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.QuestionOptionSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionOptionDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 考题选项管理 Convert
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
@Component
public interface QuestionOptionConvert {

    QuestionOptionConvert INSTANCE = Mappers.getMapper(QuestionOptionConvert.class);

    QuestionOptionDO convert(QuestionOptionSaveReqVO bean);

    QuestionOptionRespVO convert(QuestionOptionDO bean);

    List<QuestionOptionRespVO> convertList(List<QuestionOptionDO> list);

    List<QuestionOptionDO> convertDOList(List<QuestionOptionSaveReqVO> list);
}
