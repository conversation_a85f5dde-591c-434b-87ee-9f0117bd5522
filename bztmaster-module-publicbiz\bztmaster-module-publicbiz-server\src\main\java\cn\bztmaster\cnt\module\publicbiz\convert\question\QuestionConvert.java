package cn.bztmaster.cnt.module.publicbiz.convert.question;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.api.question.dto.QuestionRespDTO;
import cn.bztmaster.cnt.module.publicbiz.api.question.dto.QuestionSaveReqDTO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.QuestionRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.QuestionSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 考题管理 Convert
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
@Component
public interface QuestionConvert {

    QuestionConvert INSTANCE = Mappers.getMapper(QuestionConvert.class);

    QuestionDO convert(QuestionSaveReqVO bean);

    QuestionDO convert(QuestionSaveReqDTO bean);

    QuestionRespVO convert(QuestionDO bean);

    QuestionRespDTO convertDTO(QuestionDO bean);

    List<QuestionRespVO> convertList(List<QuestionDO> list);

    List<QuestionRespDTO> convertDTOList(List<QuestionDO> list);

    PageResult<QuestionRespVO> convertPage(PageResult<QuestionDO> page);

    PageResult<QuestionRespDTO> convertDTOPage(PageResult<QuestionDO> page);
}
