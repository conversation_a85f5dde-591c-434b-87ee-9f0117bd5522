# 素材回收站接口文档

## 图片回收站列表

**接口地址**:`/system/material/image/recycleList`

**请求方式**:`GET`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称       | 参数说明               | 请求类型 | 是否必须 | 数据类型 | schema |
| -------------- | ---------------------- | -------- | -------- | -------- | ------ |
| pageNo         | 页码，从 1 开始        | query    | true     | string   |        |
| pageSize       | 每页条数，最大值为 100 | query    | true     | string   |        |
| name           | 图片名称，模糊搜索     | query    | false    | string   |        |
| sourceOrgId    | 来源机构ID             | query    | false    | string   |        |
| categoryId     | 分类ID                 | query    | false    | string   |        |
| visibleOrgId   | 可视范围机构ID         | query    | false    | string   |        |
| visibleOrgName | 可视范围机构名称       | query    | false    | string   |        |
| updateTimeFrom |                        | query    | false    | string   |        |
| updateTimeTo   |                        | query    | false    | string   |        |

**响应状态**:

| 状态码 | 说明 | schema                            |
| ------ | ---- | --------------------------------- |
| 200    | OK   | CommonResultPageResultImageRespVO |

**响应参数**:

| 参数名称 | 参数说明 | 类型 | schema |
| --- | --- | --- | --- |
| code |  | integer(int32) | integer(int32) |
| data |  | PageResultImageRespVO | PageResultImageRespVO |
| &emsp;&emsp;list | 数据 | array | ImageRespVO |
| &emsp;&emsp;&emsp;&emsp;id | 图片ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;name | 图片名称 | string |  |
| &emsp;&emsp;&emsp;&emsp;url | 图片URL | string |  |
| &emsp;&emsp;&emsp;&emsp;sourceOrgId | 来源机构ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;sourceOrgName | 来源机构名称 | string |  |
| &emsp;&emsp;&emsp;&emsp;categoryId | 分类ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;createTime | 创建时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;visibleOrgId | 可视范围机构ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;visibleOrgName | 可视范围机构名称 | string |  |
| &emsp;&emsp;total | 总量 | integer(int64) |  |
| msg |  | string |  |

**响应示例**:

```javascript
{
	"code": 0,
	"data": {
		"list": [
			{
				"id": 1,
				"name": "示例图片",
				"url": "https://example.com/image.jpg",
				"sourceOrgId": 1,
				"sourceOrgName": "内部素材库",
				"categoryId": 1,
				"createTime": "",
				"visibleOrgId": 0,
				"visibleOrgName": ""
			}
		],
		"total": 0
	},
	"msg": ""
}
```

## 图片回收站永久删除

**接口地址**:`/system/material/image/recycleDelete`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
;[]
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| -------- | -------- | -------- | -------- | -------- | ------ |
| integers | integer  | body     | true     | array    |        |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 图片回收站恢复

**接口地址**:`/system/material/image/recycleRestore`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
;[]
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| -------- | -------- | -------- | -------- | -------- | ------ |
| integers | integer  | body     | true     | array    |        |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 视频回收站列表

**接口地址**:`/system/material/video/recycleList`

**请求方式**:`GET`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称       | 参数说明               | 请求类型 | 是否必须 | 数据类型 | schema |
| -------------- | ---------------------- | -------- | -------- | -------- | ------ |
| pageNo         | 页码，从 1 开始        | query    | true     | string   |        |
| pageSize       | 每页条数，最大值为 100 | query    | true     | string   |        |
| name           | 视频名称，模糊搜索     | query    | false    | string   |        |
| sourceOrgId    | 来源机构ID             | query    | false    | string   |        |
| categoryId     | 分类ID                 | query    | false    | string   |        |
| visibleOrgId   | 可视范围机构ID         | query    | false    | string   |        |
| visibleOrgName | 可视范围机构名称       | query    | false    | string   |        |
| updateTimeFrom |                        | query    | false    | string   |        |
| updateTimeTo   |                        | query    | false    | string   |        |

**响应状态**:

| 状态码 | 说明 | schema                            |
| ------ | ---- | --------------------------------- |
| 200    | OK   | CommonResultPageResultVideoRespVO |

**响应参数**:

| 参数名称 | 参数说明 | 类型 | schema |
| --- | --- | --- | --- |
| code |  | integer(int32) | integer(int32) |
| data |  | PageResultVideoRespVO | PageResultVideoRespVO |
| &emsp;&emsp;list | 数据 | array | VideoRespVO |
| &emsp;&emsp;&emsp;&emsp;id | 视频ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;name | 视频名称 | string |  |
| &emsp;&emsp;&emsp;&emsp;url | 视频URL | string |  |
| &emsp;&emsp;&emsp;&emsp;thumbUrl | 缩略图URL | string |  |
| &emsp;&emsp;&emsp;&emsp;sourceOrgId | 来源机构ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;sourceOrgName | 来源机构名称 | string |  |
| &emsp;&emsp;&emsp;&emsp;categoryId | 分类ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;createTime | 创建时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;visibleOrgId | 可视范围机构ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;visibleOrgName | 可视范围机构名称 | string |  |
| &emsp;&emsp;total | 总量 | integer(int64) |  |
| msg |  | string |  |

**响应示例**:

```javascript
{
	"code": 0,
	"data": {
		"list": [
			{
				"id": 1,
				"name": "示例视频",
				"url": "https://example.com/video.mp4",
				"thumbUrl": "https://example.com/thumb.jpg",
				"sourceOrgId": 1,
				"sourceOrgName": "内部素材库",
				"categoryId": 1,
				"createTime": "",
				"visibleOrgId": 0,
				"visibleOrgName": ""
			}
		],
		"total": 0
	},
	"msg": ""
}
```

## 视频回收站恢复

**接口地址**:`/system/material/video/recycleRestore`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
;[]
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| -------- | -------- | -------- | -------- | -------- | ------ |
| integers | integer  | body     | true     | array    |        |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 视频回收站永久删除

**接口地址**:`/system/material/video/recycleDelete`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
;[]
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| -------- | -------- | -------- | -------- | -------- | ------ |
| integers | integer  | body     | true     | array    |        |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 文章回收站列表

**接口地址**:`/system/material/article/recycleList`

**请求方式**:`GET`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称       | 参数说明               | 请求类型 | 是否必须 | 数据类型 | schema |
| -------------- | ---------------------- | -------- | -------- | -------- | ------ |
| pageNo         | 页码，从 1 开始        | query    | true     | string   |        |
| pageSize       | 每页条数，最大值为 100 | query    | true     | string   |        |
| title          | 文章标题，模糊搜索     | query    | false    | string   |        |
| sourceOrgId    | 来源机构ID             | query    | false    | string   |        |
| categoryId     | 分类ID                 | query    | false    | string   |        |
| visibleOrgId   | 可视范围机构ID         | query    | false    | string   |        |
| visibleOrgName | 可视范围机构名称       | query    | false    | string   |        |
| updateTimeFrom |                        | query    | false    | string   |        |
| updateTimeTo   |                        | query    | false    | string   |        |

**响应状态**:

| 状态码 | 说明 | schema                              |
| ------ | ---- | ----------------------------------- |
| 200    | OK   | CommonResultPageResultArticleRespVO |

**响应参数**:

| 参数名称 | 参数说明 | 类型 | schema |
| --- | --- | --- | --- |
| code |  | integer(int32) | integer(int32) |
| data |  | PageResultArticleRespVO | PageResultArticleRespVO |
| &emsp;&emsp;list | 数据 | array | ArticleRespVO |
| &emsp;&emsp;&emsp;&emsp;id | 文章ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;title | 文章标题 | string |  |
| &emsp;&emsp;&emsp;&emsp;content | 文章内容 | string |  |
| &emsp;&emsp;&emsp;&emsp;coverUrl | 封面图片URL | string |  |
| &emsp;&emsp;&emsp;&emsp;sourceOrgId | 来源机构ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;sourceOrgName | 来源机构名称 | string |  |
| &emsp;&emsp;&emsp;&emsp;categoryId | 分类ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;createTime | 创建时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;visibleOrgId | 可视范围机构ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;visibleOrgName | 可视范围机构名称 | string |  |
| &emsp;&emsp;total | 总量 | integer(int64) |  |
| msg |  | string |  |

**响应示例**:

```javascript
{
	"code": 0,
	"data": {
		"list": [
			{
				"id": 1,
				"title": "示例文章",
				"content": "这是文章的内容...",
				"coverUrl": "https://example.com/cover.jpg",
				"sourceOrgId": 1,
				"sourceOrgName": "内部素材库",
				"categoryId": 1,
				"createTime": "",
				"visibleOrgId": 0,
				"visibleOrgName": ""
			}
		],
		"total": 0
	},
	"msg": ""
}
```

## 文章回收站永久删除

**接口地址**:`/system/material/article/recycleDelete`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
;[]
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| -------- | -------- | -------- | -------- | -------- | ------ |
| integers | integer  | body     | true     | array    |        |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 文章回收站恢复

**接口地址**:`/system/material/article/recycleRestore`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
;[]
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| -------- | -------- | -------- | -------- | -------- | ------ |
| integers | integer  | body     | true     | array    |        |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 文档回收站列表

**接口地址**:`/system/material/document/recycleList`

**请求方式**:`GET`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称       | 参数说明               | 请求类型 | 是否必须 | 数据类型 | schema |
| -------------- | ---------------------- | -------- | -------- | -------- | ------ |
| pageNo         | 页码，从 1 开始        | query    | true     | string   |        |
| pageSize       | 每页条数，最大值为 100 | query    | true     | string   |        |
| documentName   | 文档名称，模糊搜索     | query    | false    | string   |        |
| sourceOrgId    | 来源机构ID             | query    | false    | string   |        |
| categoryId     | 分类ID                 | query    | false    | string   |        |
| visibleOrgId   | 可视范围机构ID         | query    | false    | string   |        |
| visibleOrgName | 可视范围机构名称       | query    | false    | string   |        |
| updateTimeFrom |                        | query    | false    | string   |        |
| updateTimeTo   |                        | query    | false    | string   |        |

**响应状态**:

| 状态码 | 说明 | schema                               |
| ------ | ---- | ------------------------------------ |
| 200    | OK   | CommonResultPageResultDocumentRespVO |

**响应参数**:

| 参数名称 | 参数说明 | 类型 | schema |
| --- | --- | --- | --- |
| code |  | integer(int32) | integer(int32) |
| data |  | PageResultDocumentRespVO | PageResultDocumentRespVO |
| &emsp;&emsp;list | 数据 | array | DocumentRespVO |
| &emsp;&emsp;&emsp;&emsp;id | 文档ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;documentName | 文档名称 | string |  |
| &emsp;&emsp;&emsp;&emsp;documentUrl | 文档URL | string |  |
| &emsp;&emsp;&emsp;&emsp;sourceOrgId | 来源机构ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;sourceOrgName | 来源机构名称 | string |  |
| &emsp;&emsp;&emsp;&emsp;categoryId | 分类ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;createTime | 创建时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;visibleOrgId | 可视范围机构ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;visibleOrgName | 可视范围机构名称 | string |  |
| &emsp;&emsp;total | 总量 | integer(int64) |  |
| msg |  | string |  |

**响应示例**:

```javascript
{
	"code": 0,
	"data": {
		"list": [
			{
				"id": 1,
				"documentName": "示例文档",
				"documentUrl": "https://example.com/document.pdf",
				"sourceOrgId": 1,
				"sourceOrgName": "内部素材库",
				"categoryId": 1,
				"createTime": "",
				"visibleOrgId": 0,
				"visibleOrgName": ""
			}
		],
		"total": 0
	},
	"msg": ""
}
```

## 文档回收站永久删除

**接口地址**:`/system/material/document/recycleDelete`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
;[]
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| -------- | -------- | -------- | -------- | -------- | ------ |
| integers | integer  | body     | true     | array    |        |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 文档回收站恢复

**接口地址**:`/system/material/document/recycleRestore`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
;[]
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| -------- | -------- | -------- | -------- | -------- | ------ |
| integers | integer  | body     | true     | array    |        |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 图文回收站列表

**接口地址**:`/system/material/news/recycleList`

**请求方式**:`GET`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称       | 参数说明               | 请求类型 | 是否必须 | 数据类型 | schema |
| -------------- | ---------------------- | -------- | -------- | -------- | ------ |
| pageNo         | 页码，从 1 开始        | query    | true     | string   |        |
| pageSize       | 每页条数，最大值为 100 | query    | true     | string   |        |
| name           | 图文名称，模糊搜索     | query    | false    | string   |        |
| sourceOrgId    | 来源机构ID             | query    | false    | string   |        |
| categoryId     | 分类ID                 | query    | false    | string   |        |
| visibleOrgId   | 可视范围机构ID         | query    | false    | string   |        |
| visibleOrgName | 可视范围机构名称       | query    | false    | string   |        |
| updateTimeFrom |                        | query    | false    | string   |        |
| updateTimeTo   |                        | query    | false    | string   |        |

**响应状态**:

| 状态码 | 说明 | schema                           |
| ------ | ---- | -------------------------------- |
| 200    | OK   | CommonResultPageResultNewsRespVO |

**响应参数**:

| 参数名称 | 参数说明 | 类型 | schema |
| --- | --- | --- | --- |
| code |  | integer(int32) | integer(int32) |
| data |  | PageResultNewsRespVO | PageResultNewsRespVO |
| &emsp;&emsp;list | 数据 | array | NewsRespVO |
| &emsp;&emsp;&emsp;&emsp;id | 图文ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;name | 图文名称 | string |  |
| &emsp;&emsp;&emsp;&emsp;categoryId | 分类ID，关联mp_material_category.id | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;mediaId | 微信媒体ID | string |  |
| &emsp;&emsp;&emsp;&emsp;content | 图文内容，文案内容 | string |  |
| &emsp;&emsp;&emsp;&emsp;thumbUrl | 缩略图URL | string |  |
| &emsp;&emsp;&emsp;&emsp;articleCount | 文章数量 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;sourceType | 素材来源类型，1-本地上传，2-微信同步，3-外部链接 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;sourceOrgId | 来源机构ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;sourceOrgName | 来源机构名称 | string |  |
| &emsp;&emsp;&emsp;&emsp;accountId | 公众号账号ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;status | 状态，0-草稿，1-已发布，2-已下线 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;publishTime | 发布时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;tags | 标签，多个标签用逗号分隔 | string |  |
| &emsp;&emsp;&emsp;&emsp;description | 图文描述 | string |  |
| &emsp;&emsp;&emsp;&emsp;isPermanent | 是否永久素材，0-临时，1-永久 | boolean |  |
| &emsp;&emsp;&emsp;&emsp;expireTime | 过期时间（临时素材） | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;newsType | 图文类型，1-已发布，2-草稿 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;visibleOrgId | 可视范围机构ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;visibleOrgName | 可视范围机构名称 | string |  |
| &emsp;&emsp;&emsp;&emsp;tenantId | 租户ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;creator | 创建人 | string |  |
| &emsp;&emsp;&emsp;&emsp;createTime | 创建时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;updater | 更新人 | string |  |
| &emsp;&emsp;&emsp;&emsp;updateTime | 更新时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;deleted | 是否删除，0-未删除，1-已删除 | boolean |  |
| &emsp;&emsp;total | 总量 | integer(int64) |  |
| msg |  | string |  |

**响应示例**:

```javascript
{
	"code": 0,
	"data": {
		"list": [
			{
				"id": 1,
				"name": "示例图文",
				"categoryId": 0,
				"mediaId": "",
				"content": "",
				"thumbUrl": "",
				"articleCount": 0,
				"sourceType": 0,
				"sourceOrgId": 1,
				"sourceOrgName": "内部素材库",
				"accountId": 0,
				"status": 0,
				"publishTime": "",
				"tags": "",
				"description": "",
				"isPermanent": true,
				"expireTime": "",
				"newsType": 0,
				"visibleOrgId": 0,
				"visibleOrgName": "",
				"tenantId": 0,
				"creator": "",
				"createTime": "",
				"updater": "",
				"updateTime": "",
				"deleted": true
			}
		],
		"total": 0
	},
	"msg": ""
}
```

## 图文回收站永久删除

**接口地址**:`/system/material/news/recycleDelete`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
;[]
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| -------- | -------- | -------- | -------- | -------- | ------ |
| integers | integer  | body     | true     | array    |        |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 图文回收站恢复

**接口地址**:`/system/material/news/recycleRestore`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
;[]
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| -------- | -------- | -------- | -------- | -------- | ------ |
| integers | integer  | body     | true     | array    |        |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```
