<template>
  <el-dialog :model-value="visible" :title="dialogTitle" width="700px" :close-on-click-modal="false" @close="onClose">
    <div class="talent-create-step">
      <el-steps :active="createStep" align-center>
        <el-step
          v-for="(item, idx) in stepList"
          :key="item.title"
          :title="item.title"
          :class="['talent-step-item', { clickable: true, active: createStep === idx + 1 }]"
          @click="handleStepClick(idx + 1)"
        />
      </el-steps>
    </div>
    <div v-if="createStep === 1" class="talent-create-form">
      <div class="section-title">A. 核心用户表 (USER)</div>
      <el-form :model="createForm" :rules="createFormRules" label-width="80px" ref="createFormRef" class="talent-form-grid">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名" prop="name" required>
              <el-input v-model="createForm.name" placeholder="请输入姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone" required>
              <el-input v-model="createForm.phone" placeholder="请输入11位手机号" maxlength="11" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="身份证" prop="identityId" required>
              <el-input v-model="createForm.identityId" placeholder="请输入18位身份证" maxlength="18" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email" required>
              <el-input v-model="createForm.email" placeholder="请输入电子邮箱" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-select v-model="createForm.gender" placeholder="请选择性别">
                <el-option label="男" value="男" />
                <el-option label="女" value="女" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出生日期" prop="birthday">
              <el-date-picker v-model="createForm.birthday" type="date" placeholder="年/月/日" style="width: 100%;" :disabled-date="disabledBirthdayDate" />
            </el-form-item>
          </el-col>

          <!-- 新增：所属机构、人才来源、平台自营 -->
          <el-col :span="12">
            <el-form-item label-width="110px" prop="orgId" label="所属机构">
              <el-select v-model="createForm.orgId" placeholder="请选择所属机构" filterable clearable>
                <el-option v-for="item in orgOptions" :key="item.id" :label="item.label" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label-width="110px" prop="talentSource">
              <template #label>
                人才来源
                <el-tooltip placement="top" effect="dark">
                  <template #content>
                    来源说明：<br />
                    • 平台录入：平台运营人员手动录入<br />
                    • 机构录入：合作机构导入的人才信息<br />
                    • 个人申报：个人自主申报或移动端注册<br />
                    • 政政数据对接：通过数据交换/程序注册<br />
                    • 政银数据对接：通过数据交换/业务注册<br />
                    • 银行App：通过银行App等外部渠道注册
                  </template>
                  <el-icon style="margin-left: 4px; color: #999; cursor: pointer; vertical-align: middle;">
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </template>
              <el-select v-model="createForm.talentSource" placeholder="请选择人才来源" filterable clearable style="width: 220px">
                <el-option v-for="item in talentSourceOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="平台自营">
              <el-checkbox v-model="createForm.isSelfSupport">是否为平台自营阿姨</el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="人才标签">
              <el-input
                v-model="createForm.tagsInput"
                placeholder="添加标签...（输入后按回车添加）"
                @keyup.enter="addTag"
                clearable
              />
              <div class="talent-tags-list">
                <el-tag
                  v-for="(tag, idx) in createForm.tags"
                  :key="tag"
                  closable
                  @close="removeTag(idx)"
                  type="info"
                  style="margin-right: 6px; margin-top: 6px;"
                >{{ tag }}</el-tag>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <div class="recommend-title">推荐标签:</div>
            <div class="recommend-tags">
              <el-tag
                v-for="tag in recommendTags"
                :key="tag"
                :type="createForm.tags.includes(tag) ? 'success' : 'default'"
                @click="addRecommendTag(tag)"
                class="recommend-tag"
              >{{ tag }}</el-tag>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div style="text-align:right; margin-top: 20px;">
        <el-button type="primary" @click="nextStep">下一步</el-button>
      </div>
    </div>
    <div v-else-if="createStep === 2" class="talent-create-form">
      <div class="section-title">B. 教育背景 (EDUCATION)</div>
      <div v-for="(edu, idx) in educationForm" :key="idx" style="position:relative; margin-bottom: 18px;">
        <el-form :model="edu" label-width="100px" ref="educationFormRef" class="talent-form-grid">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="毕业院校" prop="school">
                <el-input v-model="edu.school" placeholder="例如：北京大学" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="专业" prop="major">
                <el-input v-model="edu.major" placeholder="例如：计算机科学与技术" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="学位类型" prop="degreeType">
                <el-select v-model="edu.degreeType" placeholder="请选择">
                  <el-option label="博士" value="博士" />
                  <el-option label="硕士" value="硕士" />
                  <el-option label="本科" value="本科" />
                  <el-option label="大专" value="大专" />
                  <el-option label="中专" value="中专" />
                  <el-option label="高中及以下" value="高中及以下" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="学业排名" prop="rank">
                <el-input v-model="edu.rank" placeholder="例如：Top 5%" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="在校开始时间" prop="startTime">
                <el-date-picker v-model="edu.startTime" type="date" placeholder="年/月/日" style="width: 100%;" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="在校结束时间" prop="endTime">
                <el-date-picker v-model="edu.endTime" type="date" placeholder="年/月/日" style="width: 100%;" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-button v-if="educationForm.length > 1" type="danger" size="small" style="position:absolute; top:0; right:0;" @click="removeEducation(idx)">删除</el-button>
        </el-form>
        <el-divider v-if="idx < educationForm.length - 1" />
      </div>
      <el-divider />
      <div style="text-align:right; margin-bottom: 10px;">
        <el-button type="primary" plain icon="el-icon-plus" @click="addEducation">添加一项</el-button>
      </div>
      <div style="text-align:right; margin-top: 20px;">
        <el-button @click="prevStep">上一步</el-button>
        <el-button type="primary" @click="nextStep">下一步</el-button>
      </div>
    </div>
    <div v-else-if="createStep === 3" class="talent-create-form">
      <div class="section-title">C. 实践与项目 (PRACTICE & PROJECT)</div>
      <!-- 校内实践 -->
      <div style="font-weight:bold; margin-bottom:8px;">校内实践 (CAMPUS_PRACTICE)</div>
      <div v-for="(p, idx) in campusPracticeForm" :key="'campus'+idx" style="position:relative; margin-bottom: 18px;">
        <el-form :model="p" label-width="110px" class="talent-form-grid">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="实践名称" prop="practiceName">
                <el-select v-model="p.practiceName" placeholder="请选择实践名称">
                  <el-option label="实践A" value="实践A" />
                  <el-option label="实践B" value="实践B" />
                  <el-option label="实践C" value="实践C" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="组织方" prop="practiceOrg">
                <el-input v-model="p.practiceOrg" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="实践开始时间" prop="practiceStart">
                <el-date-picker v-model="p.practiceStart" type="date" placeholder="年/月/日" style="width: 100%;" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="实践结束时间" prop="practiceEnd">
                <el-date-picker v-model="p.practiceEnd" type="date" placeholder="年/月/日" style="width: 100%;" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="实践报告/总结" prop="practiceSummary">
                <el-input v-model="p.practiceSummary" type="textarea" :rows="2" placeholder="" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-button v-if="campusPracticeForm.length > 1" type="danger" size="small" style="position:absolute; top:0; right:0;" @click="removeCampusPractice(idx)">删除</el-button>
        </el-form>
        <el-divider v-if="idx < campusPracticeForm.length - 1" />
      </div>
      <el-divider />
      <div style="text-align:right; margin-bottom: 10px;">
        <el-button type="primary" plain icon="el-icon-plus" @click="addCampusPractice">添加一项</el-button>
      </div>
      <!-- 实习经历 -->
      <div style="font-weight:bold; margin:16px 0 8px 0;">实习经历 (INTERNSHIP)</div>
      <div v-for="(i, idx) in internshipForm" :key="'intern'+idx" style="position:relative; margin-bottom: 18px;">
        <el-form :model="i" label-width="110px" class="talent-form-grid">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="实习公司" prop="internCompany">
                <el-select v-model="i.internCompany" placeholder="请选择实习公司" filterable clearable>
                  <el-option v-for="item in internCompanyOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="实习岗位" prop="internJob">
                <el-input v-model="i.internJob" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="实习开始时间" prop="internStart">
                <el-date-picker v-model="i.internStart" type="date" placeholder="年/月/日" style="width: 100%;" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="实习结束时间" prop="internEnd">
                <el-date-picker v-model="i.internEnd" type="date" placeholder="年/月/日" style="width: 100%;" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="工作职责" prop="internDuty">
                <el-input v-model="i.internDuty" type="textarea" :rows="2" placeholder="" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-button v-if="internshipForm.length > 1" type="danger" size="small" style="position:absolute; top:0; right:0;" @click="removeInternship(idx)">删除</el-button>
        </el-form>
        <el-divider v-if="idx < internshipForm.length - 1" />
      </div>
      <el-divider />
      <div style="text-align:right; margin-bottom: 10px;">
        <el-button type="primary" plain icon="el-icon-plus" @click="addInternship">添加一项</el-button>
      </div>
      <!-- 项目经历 -->
      <div style="font-weight:bold; margin:16px 0 8px 0;">项目经历 (PROJECT)</div>
      <div v-for="(pj, idx) in projectForm" :key="'pj'+idx" style="position:relative; margin-bottom: 18px;">
        <el-form :model="pj" label-width="110px" class="talent-form-grid">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="项目名称" prop="projectName">
                <el-select v-model="pj.projectName" placeholder="请选择项目名称">
                  <el-option label="项目A" value="项目A" />
                  <el-option label="项目B" value="项目B" />
                  <el-option label="项目C" value="项目C" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="项目描述" prop="projectDesc">
                <el-input v-model="pj.projectDesc" type="textarea" :rows="2" placeholder="" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-button v-if="projectForm.length > 1" type="danger" size="small" style="position:absolute; top:0; right:0;" @click="removeProject(idx)">删除</el-button>
        </el-form>
        <el-divider v-if="idx < projectForm.length - 1" />
      </div>
      <el-divider />
      <div style="text-align:right; margin-bottom: 10px;">
        <el-button type="primary" plain icon="el-icon-plus" @click="addProject">添加一项</el-button>
      </div>
      <div style="text-align:right; margin-top: 20px;">
        <el-button @click="prevStep">上一步</el-button>
        <el-button type="primary" @click="nextStep">下一步</el-button>
      </div>
    </div>
    <div v-else-if="createStep === 4" class="talent-create-form">
      <div class="section-title">D. 培训与技能 (TRAINING & SKILL)</div>
      <div style="font-weight:bold; margin-bottom:8px;">培训记录 (TRAINING)</div>
      <div v-for="(t, idx) in trainingFormList" :key="'train'+idx" style="position:relative; margin-bottom: 18px;">
        <el-form :model="t" label-width="100px" class="talent-form-grid">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="培训机构" prop="org">
                <el-input v-model="t.org" placeholder="例如：汇成平台" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="课程名称" prop="course">
                <el-input v-model="t.course" placeholder="例如：金牌月嫂培训" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="完成日期" prop="finishDate">
                <el-date-picker v-model="t.finishDate" type="date" placeholder="年/月/日" style="width: 100%;" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-button v-if="trainingFormList.length > 1" type="danger" size="small" style="position:absolute; top:0; right:0;" @click="removeTraining(idx)">删除</el-button>
        </el-form>
        <el-divider v-if="idx < trainingFormList.length - 1" />
      </div>
      <el-divider />
      <div style="text-align:right; margin-bottom: 10px;">
        <el-button type="primary" plain icon="el-icon-plus" @click="addTraining">添加一项</el-button>
      </div>
      <div style="font-weight:bold; margin:16px 0 8px 0;">技能清单 (SKILL)</div>
      <div v-for="(s, idx) in skillFormList" :key="'skill'+idx" style="position:relative; margin-bottom: 18px;">
        <el-form :model="s" label-width="100px" class="talent-form-grid">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="技能名称" prop="skillName">
                <el-input v-model="s.skillName" placeholder="例如：深度保洁" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="掌握程度" prop="skillLevel">
                <el-select v-model="s.skillLevel" placeholder="请选择掌握程度">
                  <el-option label="了解" value="了解" />
                  <el-option label="熟悉" value="熟悉" />
                  <el-option label="精通" value="精通" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-button v-if="skillFormList.length > 1" type="danger" size="small" style="position:absolute; top:0; right:0;" @click="removeSkill(idx)">删除</el-button>
        </el-form>
        <el-divider v-if="idx < skillFormList.length - 1" />
      </div>
      <el-divider />
      <div style="text-align:right; margin-bottom: 10px;">
        <el-button type="primary" plain icon="el-icon-plus" @click="addSkill">添加一项</el-button>
      </div>
      <div style="text-align:right; margin-top: 20px;">
        <el-button @click="prevStep">上一步</el-button>
        <el-button type="primary" @click="nextStep">下一步</el-button>
      </div>
    </div>
    <div v-else-if="createStep === 5" class="talent-create-form">
      <div class="section-title">E. 认证与资质 (CERTIFICATE)</div>
      <div v-for="(c, idx) in certificateFormList" :key="'cert'+idx" style="position:relative; margin-bottom: 18px;">
        <el-form :model="c" label-width="100px" class="talent-form-grid">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="证书名称" prop="certName">
                <el-input v-model="c.certName" placeholder="例如：高级母婴护理师证" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="证书编号" prop="certNo">
                <el-input v-model="c.certNo" placeholder="请输入证书编号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="发证机构" prop="certOrg">
                <el-input v-model="c.certOrg" placeholder="例如：XX市人社局" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="颁发日期" prop="issueDate">
                <el-date-picker v-model="c.issueDate" type="date" placeholder="年/月/日" style="width: 100%;" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="到期时间" prop="expireDate">
                <el-date-picker v-model="c.expireDate" type="date" placeholder="年/月/日" style="width: 100%;" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="记录来源" prop="source">
                <el-select v-model="c.source" placeholder="请选择记录来源">
                  <el-option label="平台录入" value="平台录入" />
                  <el-option label="机构录入" value="机构录入" />
                  <el-option label="个人申报" value="个人申报" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="审核状态" prop="status">
                <el-select v-model="c.status" placeholder="请选择审核状态">
                  <el-option label="待认证" value="待认证" />
                  <el-option label="已认证" value="已认证" />
                  <el-option label="已驳回" value="已驳回" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="证书图片" prop="certificateImageUrl">
                <el-upload
                  class="upload-demo"
                  action="#"
                  :auto-upload="false"
                  :show-file-list="true"
                  :limit="1"
                  :on-change="file => handleCertImageChange(file, idx)"
                  :file-list="c.certificateImageUrl ? [{ url: c.certificateImageUrl, name: '证书图片' }] : []"
                  list-type="picture-card"
                >
                  <el-icon><Plus /></el-icon>
                  <template #tip>
                    <div style="color: #888; margin-left: 10px; display: inline-block;">仅支持上传1张图片，上传后自动填充URL</div>
                  </template>
                </el-upload>
                <div v-if="c.certificateImageUrl" style="margin-top: 8px;">
                  <el-image :src="c.certificateImageUrl" style="width: 80px; height: 80px; object-fit: contain; border: 1px solid #eee;" :preview-src-list="[c.certificateImageUrl]" />
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-button v-if="certificateFormList.length > 1" type="danger" size="small" style="position:absolute; top:0; right:0;" @click="removeCertificate(idx)">删除</el-button>
        </el-form>
        <el-divider v-if="idx < certificateFormList.length - 1" />
      </div>
      <el-divider />
      <div style="text-align:right; margin-bottom: 10px;">
        <el-button type="primary" plain icon="el-icon-plus" @click="addCertificate">添加一项</el-button>
      </div>
      <div style="text-align:right; margin-top: 20px;">
        <el-button @click="prevStep">上一步</el-button>
        <el-button type="primary" @click="nextStep">下一步</el-button>
      </div>
    </div>
    <div v-else-if="createStep === 6" class="talent-create-form">
      <div class="section-title">F. 求职与工作履历 (APPLICATION & EMPLOYMENT)</div>
      <div style="font-weight:bold; margin-bottom:8px;">工作履历 (EMPLOYMENT)</div>
      <div v-for="(em, idx) in employmentFormList" :key="'employ'+idx" style="position:relative; margin-bottom: 18px;">
        <el-form :model="em" label-width="110px" class="talent-form-grid">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="就业公司" prop="company">
                <el-input v-model="em.company" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="职位" prop="position">
                <el-input v-model="em.position" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="任职开始时间" prop="startTime">
                <el-date-picker v-model="em.startTime" type="date" placeholder="年/月/日" style="width: 100%;" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="任职结束时间" prop="endTime">
                <el-date-picker v-model="em.endTime" type="date" placeholder="年/月/日" style="width: 100%;" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="薪资 (月薪, 元)" prop="salary">
                <el-input v-model="em.salary" placeholder="例如: 8000" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-button v-if="employmentFormList.length > 1" type="danger" size="small" style="position:absolute; top:0; right:0;" @click="removeEmployment(idx)">删除</el-button>
        </el-form>
        <el-divider v-if="idx < employmentFormList.length - 1" />
      </div>
      <el-divider />
      <div style="text-align:right; margin-bottom: 10px;">
        <el-button type="primary" plain icon="el-icon-plus" @click="addEmployment">添加一项</el-button>
      </div>
      <div style="font-weight:bold; margin:16px 0 8px 0;">求职记录 (JOB_APPLICATION)</div>
      <div v-for="(ja, idx) in jobApplicationFormList" :key="'jobapp'+idx" style="position:relative; margin-bottom: 18px;">
        <el-form :model="ja" label-width="110px" class="talent-form-grid">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="申请公司" prop="applyCompany">
                <el-input v-model="ja.applyCompany" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="申请职位" prop="applyPosition">
                <el-input v-model="ja.applyPosition" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="申请日期" prop="applyDate">
                <el-date-picker v-model="ja.applyDate" type="date" placeholder="年/月/日" style="width: 100%;" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="状态" prop="applyStatus">
                <el-select v-model="ja.applyStatus" placeholder="请选择状态">
                  <el-option label="已投递" value="已投递" />
                  <el-option label="面试中" value="面试中" />
                  <el-option label="已录用" value="已录用" />
                  <el-option label="不合适" value="不合适" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-button v-if="jobApplicationFormList.length > 1" type="danger" size="small" style="position:absolute; top:0; right:0;" @click="removeJobApplication(idx)">删除</el-button>
        </el-form>
        <el-divider v-if="idx < jobApplicationFormList.length - 1" />
      </div>
      <el-divider />
      <div style="text-align:right; margin-bottom: 10px;">
        <el-button type="primary" plain icon="el-icon-plus" @click="addJobApplication">添加一项</el-button>
      </div>
      <div style="text-align:right; margin-top: 20px;">
        <el-button @click="prevStep">上一步</el-button>
        <el-button type="primary" @click="onSave">保存</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineProps, defineEmits, watch, watchEffect } from 'vue'
import { ElMessage } from 'element-plus'
import { TalentPoolApi } from '@/api/apachecore/talentpool/index'
import dayjs from 'dayjs'
import axios from 'axios'
import request from '@/config/axios'
import { getDictDataPage } from '@/api/system/dict/dict.data'
import { getSimpleDeptList } from '@/api/system/dept'
import { getDictDataByType } from '@/api/system/dict/dict.data'
import { QuestionFilled } from '@element-plus/icons-vue'

const props = defineProps<{ modelValue: boolean, talent?: any }>()
const emit = defineEmits(['update:modelValue', 'saved'])

const visible = computed({
  get: () => props.modelValue,
  set: (val: boolean) => emit('update:modelValue', val)
})

const dialogTitle = computed(() => props.talent && props.talent.id ? '编辑人才档案' : '新建人才档案')

const createStep = ref(1)
const createForm = reactive({
  name: '',
  phone: '',
  identityId: '',
  email: '',
  gender: '男',
  birthday: '',
  orgId: '', // 新增
  talentSource: '', // 新增
  isSelfSupport: false, // 新增
  tagsInput: '',
  tags: [] as string[],
})
// 机构下拉数据
const orgOptions = ref<any[]>([])
async function loadOrgOptions() {
  const res = await getSimpleDeptList()
  const topLevels = res.filter((dept: any) => dept.parentId === 0)
  const topLevelMap = Object.fromEntries(topLevels.map((dept: any) => [dept.id, dept.name]))
  const children = res.filter((dept: any) => Object.keys(topLevelMap).map(Number).includes(dept.parentId))
  orgOptions.value = children.map(child => ({ label: `${topLevelMap[child.parentId]}/${child.name}`, id: child.id }))
}
loadOrgOptions()
// 人才来源下拉数据
const talentSourceOptions = ref<any[]>([])
async function loadTalentSourceOptions() {
  const res = await getDictDataPage({
    dictType: 'talent_sources',
    status: 0,
    pageNo: 1,
    pageSize: 99
  } as any)
  let list: any[] = []
  if (res && res.data && Array.isArray(res.data.list)) {
    list = res.data.list
  } else if (res && Array.isArray(res.list)) {
    list = res.list
  }
  talentSourceOptions.value = list.map((item: any) => ({ label: item.label, value: item.value }))
}
loadTalentSourceOptions()
// 实习公司下拉数据
const internCompanyOptions = ref<any[]>([])
async function loadInternCompanyOptions() {
  try {
    const res = await request.get({ url: '/publicbiz/partner/list/active' })
    let list = []
    if (res && res.data && Array.isArray(res.data.list)) {
      list = res.data.list
    } else if (res && Array.isArray(res.list)) {
      list = res.list
    }
    internCompanyOptions.value = list.map((item: any) => ({ label: item.name, value: item.id }))
  } catch (e) {
    internCompanyOptions.value = []
  }
}
loadInternCompanyOptions()
const createFormRef = ref()
const createFormRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' }
  ],
  identityId: [
    { required: true, message: '请输入身份证', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback();
          return;
        }
        // 格式校验
        const reg = /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/;
        if (!reg.test(value)) {
          callback(new Error('身份证格式不正确'));
          return;
        }
        // 校验码校验
        // const factors = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
        // const parity = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
        // const code = value.toUpperCase();
        // let sum = 0;
        // for (let i = 0; i < 17; i++) {
        //   sum += parseInt(code[i], 10) * factors[i];
        // }
        // const last = parity[sum % 11];
        // if (last !== code[17]) {
        //   callback(new Error('身份证校验码不正确'));
        //   return;
        // }
        callback();
      },
      trigger: 'blur'
    }
  ],
  email: [
    { required: true, message: '请输入电子邮箱', trigger: 'blur' }
  ]
}
// 教育背景支持多项
const educationForm = ref([
  {
    school: '',
    major: '',
    degreeType: '',
    rank: '',
    startTime: '',
    endTime: ''
  }
])
const educationFormRef = ref()

function addEducation() {
  educationForm.value.push({
    school: '',
    major: '',
    degreeType: '',
    rank: '',
    startTime: '',
    endTime: ''
  })
}
function removeEducation(idx: number) {
  if (educationForm.value.length > 1) {
    educationForm.value.splice(idx, 1)
  }
}
// 校内实践、实习经历、项目经历支持多项
const campusPracticeForm = ref([
  {
    practiceName: '',
    practiceOrg: '',
    practiceStart: '',
    practiceEnd: '',
    practiceSummary: ''
  }
])
const internshipForm = ref([
  {
    internCompany: '' as number | '', // 类型修正
    internJob: '',
    internStart: '',
    internEnd: '',
    internDuty: ''
  }
])
const projectForm = ref([
  {
    projectName: '',
    projectDesc: ''
  }
])
function addCampusPractice() {
  campusPracticeForm.value.push({
    practiceName: '',
    practiceOrg: '',
    practiceStart: '',
    practiceEnd: '',
    practiceSummary: ''
  })
}
function removeCampusPractice(idx: number) {
  if (campusPracticeForm.value.length > 1) campusPracticeForm.value.splice(idx, 1)
}
function addInternship() {
  internshipForm.value.push({
    internCompany: '',
    internJob: '',
    internStart: '',
    internEnd: '',
    internDuty: ''
  })
}
function removeInternship(idx: number) {
  if (internshipForm.value.length > 1) internshipForm.value.splice(idx, 1)
}
function addProject() {
  projectForm.value.push({
    projectName: '',
    projectDesc: ''
  })
}
function removeProject(idx: number) {
  if (projectForm.value.length > 1) projectForm.value.splice(idx, 1)
}
const practiceForm = reactive({
  practiceName: '',
  practiceOrg: '',
  practiceStart: '',
  practiceEnd: '',
  practiceSummary: '',
  internCompany: '',
  internJob: '',
  internStart: '',
  internEnd: '',
  internDuty: '',
  projectName: '',
  projectDesc: ''
})
const practiceFormRef = ref()
const recommendTags = [
  '认证:高级母婴护理', '认证:金牌月嫂', '认证:健康证', '认证:教师资格证', '技能:催乳', '技能:高级收纳',
  '技能:康复保健', '技能:Office办公', '技能:沟通能力强', '经验:育儿早', '经验:服务高端社区',
  '经验:2年+工作经验', '经验:服务30+家庭', '评价:细心', '评价:有耐心', '评价:守时',
  '学历:博士', '学历:硕士', '学历:985', '学历:本科', '学历:大学', '身份:学生', '身份:认证月嫂',
  '身份:培训学员', '身份:求职者', '已实名'
]
// 培训、技能、证书、求职、工作支持多项
const trainingFormList = ref([
  { org: '', course: '', finishDate: '' }
])
const skillFormList = ref([
  { skillName: '', skillLevel: '' }
])
const certificateFormList = ref([
  { certName: '', certNo: '', certOrg: '', issueDate: '', expireDate: '', source: '平台录入', status: '待认证', file: null as File | null, certificateImageUrl: '' }
])
const jobApplicationFormList = ref([
  { applyCompany: '', applyPosition: '', applyDate: '', applyStatus: '已投递' }
])
const employmentFormList = ref([
  { company: '', position: '', startTime: '', endTime: '', salary: '' }
])
function addTraining() {
  trainingFormList.value.push({ org: '', course: '', finishDate: '' })
}
function removeTraining(idx: number) {
  if (trainingFormList.value.length > 1) trainingFormList.value.splice(idx, 1)
}
function addSkill() {
  skillFormList.value.push({ skillName: '', skillLevel: '' })
}
function removeSkill(idx: number) {
  if (skillFormList.value.length > 1) skillFormList.value.splice(idx, 1)
}
function addCertificate() {
  certificateFormList.value.push({ certName: '', certNo: '', certOrg: '', issueDate: '', expireDate: '', source: '平台录入', status: '待认证', file: null, certificateImageUrl: '' })
}
function removeCertificate(idx: number) {
  if (certificateFormList.value.length > 1) certificateFormList.value.splice(idx, 1)
}
function addJobApplication() {
  jobApplicationFormList.value.push({ applyCompany: '', applyPosition: '', applyDate: '', applyStatus: '已投递' })
}
function removeJobApplication(idx: number) {
  if (jobApplicationFormList.value.length > 1) jobApplicationFormList.value.splice(idx, 1)
}
function addEmployment() {
  employmentFormList.value.push({ company: '', position: '', startTime: '', endTime: '', salary: '' })
}
function removeEmployment(idx: number) {
  if (employmentFormList.value.length > 1) employmentFormList.value.splice(idx, 1)
}
const trainingForm = reactive({
  org: '',
  course: '',
  finishDate: '',
  skillName: '',
  skillLevel: ''
})
const trainingFormRef = ref()
const certificateForm = reactive({
  certName: '',
  certNo: '',
  certOrg: '',
  issueDate: '',
  expireDate: '',
  source: '平台录入',
  status: '待认证',
  file: null as File | null,
  certificateImageUrl: '',
})
const certificateFormRef = ref()
const jobForm = reactive({
  company: '',
  position: '',
  startTime: '',
  endTime: '',
  salary: '',
  applyCompany: '',
  applyPosition: '',
  applyDate: '',
  applyStatus: '已投递'
})
const jobFormRef = ref()

const stepList = [
  { title: '核心身份' },
  { title: '教育背景' },
  { title: '实践与项目' },
  { title: '培训与技能' },
  { title: '认证与资质' },
  { title: '求职与工作' }
]
function handleStepClick(step: number) {
  createStep.value = step
}

watch(() => props.talent, (val) => {
  if (val && visible.value) {
    createForm.name = val.name || ''
    createForm.phone = val.phone || ''
    createForm.identityId = val.identityId || ''
    createForm.email = val.email || ''
    createForm.gender = val.gender || '男'
    createForm.birthday = val.birthday || ''
    createForm.tags = val.tags ? [...val.tags] : []
    // 其它表单项可根据需要补充
  } else if (!val && visible.value) {
    // 新建时清空
    createForm.name = ''
    createForm.phone = ''
    createForm.identityId = ''
    createForm.email = ''
    createForm.gender = '男'
    createForm.birthday = ''
    createForm.tags = []
  }
})

watch(visible, (val) => {
  if (val) {
    // 弹窗打开时初始化所有表单
    createForm.name = ''
    createForm.phone = ''
    createForm.identityId = ''
    createForm.email = ''
    createForm.gender = '男'
    createForm.birthday = ''
    createForm.tags = []
    createForm.tagsInput = ''
    educationForm.value = [{
      school: '',
      major: '',
      degreeType: '',
      rank: '',
      startTime: '',
      endTime: ''
    }]
    campusPracticeForm.value = [{
      practiceName: '',
      practiceOrg: '',
      practiceStart: '',
      practiceEnd: '',
      practiceSummary: ''
    }]
    internshipForm.value = [{
      internCompany: '',
      internJob: '',
      internStart: '',
      internEnd: '',
      internDuty: ''
    }]
    projectForm.value = [{
      projectName: '',
      projectDesc: ''
    }]
    trainingFormList.value = [{ org: '', course: '', finishDate: '' }]
    skillFormList.value = [{ skillName: '', skillLevel: '' }]
    certificateFormList.value = [{ certName: '', certNo: '', certOrg: '', issueDate: '', expireDate: '', source: '平台录入', status: '待认证', file: null, certificateImageUrl: '' }]
    jobApplicationFormList.value = [{ applyCompany: '', applyPosition: '', applyDate: '', applyStatus: '已投递' }]
    employmentFormList.value = [{ company: '', position: '', startTime: '', endTime: '', salary: '' }]
    createStep.value = 1
    loadTalentSourceOptions() // 弹窗每次打开都请求人才来源
  }
})

function addTag() {
  const val = createForm.tagsInput.trim()
  if (val && !createForm.tags.includes(val)) {
    createForm.tags.push(val)
  }
  createForm.tagsInput = ''
}
function removeTag(idx: number) {
  createForm.tags.splice(idx, 1)
}
function addRecommendTag(tag: string) {
  if (!createForm.tags.includes(tag)) {
    createForm.tags.push(tag)
  }
}
function nextStep() {
  if (createStep.value === 1) {
    createFormRef.value?.validate((valid: boolean) => {
      if (valid) {
        createStep.value++
      }
    })
  } else if (createStep.value < 6) {
    createStep.value++
  }
}
function prevStep() {
  if (createStep.value > 1) {
    createStep.value--
  }
}
function disabledBirthdayDate(date: Date) {
  return date.getTime() >= Date.now();
}
function handleCertImageChange(file, idx) {
  if (file && file.raw) {
    const formData = new FormData();
    formData.append('file', file.raw);
    formData.append('directory', 'certificate');
    request.postOriginal({
      url: '/infra/file/upload',
      data: formData,
      headers: { 'Content-Type': 'multipart/form-data' }
    }).then(res => {
      if (res.code === 0 && res.data && /^https?:\/\//.test(res.data)) {
        certificateFormList.value[idx].certificateImageUrl = res.data;
        ElMessage.success('上传成功');
      } else {
        certificateFormList.value[idx].certificateImageUrl = '';
        ElMessage.error(res.msg || '上传失败');
      }
    }).catch(() => {
      certificateFormList.value[idx].certificateImageUrl = '';
      ElMessage.error('上传失败');
    });
  }
}
async function onSave() {
  // 校验核心身份必填项
  if (!createForm.name || !createForm.phone || !createForm.identityId || !createForm.email) {
    ElMessage.error('请完善核心身份信息')
    return
  }
  // 组装接口需要的数据结构
  const selectedOrg = orgOptions.value.find(item => item.id === createForm.orgId)
  const postData = {
    ...(props.talent && props.talent.id ? { id: props.talent.id } : {}),
    name: createForm.name,
    phone: createForm.phone,
    identityId: createForm.identityId,
    email: createForm.email,
    gender: createForm.gender,
    birthDate: createForm.birthday,
    tags: createForm.tags,
    orgId: createForm.orgId,
    orgName: selectedOrg ? selectedOrg.label : '',
    talentSource: createForm.talentSource,
    isSelfSupport: createForm.isSelfSupport,
    educationList: educationForm.value.filter(e => e.school).map(e => ({
      institution: e.school,
      degreeType: e.degreeType,
      major: e.major,
      startDate: e.startTime,
      endDate: e.endTime,
      academicRanking: e.rank
    })),
    campusPracticeList: campusPracticeForm.value.filter(p => p.practiceName).map(p => ({
      practiceName: p.practiceName,
      organizer: p.practiceOrg,
      startDate: p.practiceStart,
      endDate: p.practiceEnd,
      practiceReport: p.practiceSummary
    })),
    internshipList: internshipForm.value.filter(i => i.internCompany).map(i => ({
      company: String(i.internCompany), // 传递id并转为字符串
      position: i.internJob,
      startDate: i.internStart,
      endDate: i.internEnd,
      responsibilities: i.internDuty
    })),
    projectList: projectForm.value.filter(pj => pj.projectName).map(pj => ({
      name: pj.projectName,
      description: pj.projectDesc
    })),
    trainingList: trainingFormList.value.filter(t => t.org).map(t => ({
      provider: t.org,
      course: t.course,
      completeDate: t.finishDate
    })),
    skillList: skillFormList.value.filter(s => s.skillName).map(s => ({
      name: s.skillName,
      level: s.skillLevel
    })),
    certificateList: certificateFormList.value.filter(c => c.certName).map(c => ({
      name: c.certName,
      certificateNo: c.certNo,
      issuer: c.certOrg,
      issueDate: c.issueDate,
      expiryDate: c.expireDate,
      certificateImageUrl: /^https?:\/\//.test(c.certificateImageUrl) ? c.certificateImageUrl : '',
      source: c.source,
      status: c.status
    })),
    jobApplicationList: jobApplicationFormList.value.filter(j => j.applyCompany).map(j => ({
      company: j.applyCompany,
      position: j.applyPosition,
      applyDate: j.applyDate,
      status: j.applyStatus
    })),
    employmentList: employmentFormList.value.filter(e => e.company).map(e => ({
      company: e.company,
      position: e.position,
      startDate: e.startTime,
      endDate: e.endTime,
      salary: e.salary ? Number(e.salary) : undefined
    }))
  }
  try {
    let res
    if (props.talent && props.talent.id) {
      res = await TalentPoolApi.updateTalent(postData as any)
    } else {
      res = await TalentPoolApi.createTalent(postData)
    }

   // console.log('res返回内容：', res)
    let id
    if (res?.data?.id !== undefined) {
      id = res.data.id
    } else if (res?.id !== undefined) {
      id = res.id
    } else {
      id = undefined
    }
    if (props.talent && props.talent.id) {
     
      // 编辑（更新）模式，按 code 判断
     
      if (res) {
        ElMessage.success('更新成功')
        emit('saved')
        emit('update:modelValue', false)
      } else {
        ElMessage.error('更新失败')
      }
    } else {
      // 新增模式，按 id 判断
      if (id && Number(id) > 0) {
        ElMessage.success('新增成功')
        emit('saved')
        emit('update:modelValue', false)
      } else {
        ElMessage.error('新增失败')
      }
    }
  } catch (e) {
    console.error('新增异常', e)
    ElMessage.error('请求异常')
  }
}
function onClose() {
  emit('update:modelValue', false)
}

function formatDate(val: any) {
  if (!val) return ''
  let clean = String(val).replace(/[\u4e00-\u9fa5]+/g, '').replace(/\s+/g, ' ').trim()
  const match = clean.match(/^(\d{2})[-/.](\d{1,2})[-/.](\d{1,2})/)
  if (match) {
    const year = parseInt(match[1], 10)
    const month = match[2].padStart(2, '0')
    const day = match[3].padStart(2, '0')
    return `20${year}-${month}-${day}`
  }
  const d = dayjs(clean, ['YYYY-MM-DD', 'YYYY/M/D', 'YYYY.M.D', 'YYYY年M月D日', 'YYYY-MM-DDTHH:mm:ssZ', 'YYYY-MM-DD HH:mm:ss', 'YYYY-MM-DDTHH:mm:ss', 'YYYY-MM-DDTHH:mm:ss.SSSZ'], true)
  if (d.isValid()) return d.format('YYYY-MM-DD')
  const d2 = new Date(clean)
  if (!isNaN(d2.getTime())) {
    return dayjs(d2).format('YYYY-MM-DD')
  }
  return val
}

async function fetchTalentDetailAndFill(id) {
  if (!id) return
  // 统一用 getTalentProfileDetail，保证结构一致
  const res = await TalentPoolApi.getTalentProfileDetail({ id })
  if (!res) return
  // 主表信息
  const user = res.user || {}
  createForm.name = user.name || ''
  createForm.phone = user.phone || ''
  createForm.identityId = user.identityId || ''
  createForm.email = user.email || ''
  createForm.gender = user.gender || '男'
  createForm.birthday = formatDate(user.birthDate)
  createForm.tags = (res.userTagList && res.userTagList.map(t => t.tagName)) || []
  // 新增：回显所属机构、人才来源、自营平台
  createForm.orgId = user.orgId ? Number(user.orgId) : ''
  createForm.talentSource = user.talentSource || ''
  createForm.isSelfSupport = !!user.isSelfSupport
  // 教育背景
  if (res.educationList && res.educationList.length) {
    educationForm.value = res.educationList.map(edu => ({
      school: edu.institution || '',
      major: edu.major || '',
      degreeType: edu.degreeType || '',
      rank: edu.academicRanking || '',
      startTime: formatDate(edu.startDate),
      endTime: formatDate(edu.endDate)
    }))
  } else {
    educationForm.value = [{
      school: '',
      major: '',
      degreeType: '',
      rank: '',
      startTime: '',
      endTime: ''
    }]
  }
  // 校园实践
  if (res.campusPracticeList && res.campusPracticeList.length) {
    campusPracticeForm.value = res.campusPracticeList.map(p => ({
      practiceName: p.practiceName || '',
      practiceOrg: p.organizer || '',
      practiceStart: formatDate(p.startDate),
      practiceEnd: formatDate(p.endDate),
      practiceSummary: p.practiceReport || ''
    }))
  } else {
    campusPracticeForm.value = [{
      practiceName: '',
      practiceOrg: '',
      practiceStart: '',
      practiceEnd: '',
      practiceSummary: ''
    }]
  }
  // 实习经历
  if (res.internshipList && res.internshipList.length) {
    internshipForm.value = res.internshipList.map(i => ({
      internCompany: i.company ? Number(i.company) : '', // 关键：转为数字，保证下拉框显示名称
      internJob: i.position || '',
      internStart: formatDate(i.startDate),
      internEnd: formatDate(i.endDate),
      internDuty: i.responsibilities || ''
    }))
  } else {
    internshipForm.value = [{
      internCompany: '',
      internJob: '',
      internStart: '',
      internEnd: '',
      internDuty: ''
    }]
  }
  // 项目经历
  if (res.projectList && res.projectList.length) {
    projectForm.value = res.projectList.map(pj => ({
      projectName: pj.name || '',
      projectDesc: pj.description || ''
    }))
  } else {
    projectForm.value = [{
      projectName: '',
      projectDesc: ''
    }]
  }
  // 培训
  if (res.trainingList && res.trainingList.length) {
    trainingFormList.value = res.trainingList.map(t => ({
      org: t.provider || '',
      course: t.course || '',
      finishDate: formatDate(t.completeDate)
    }))
  } else {
    trainingFormList.value = [{ org: '', course: '', finishDate: '' }]
  }
  // 技能
  if (res.skillList && res.skillList.length) {
    skillFormList.value = res.skillList.map(s => ({
      skillName: s.name || '',
      skillLevel: s.level || ''
    }))
  } else {
    skillFormList.value = [{ skillName: '', skillLevel: '' }]
  }
  // 证书
  if (res.certificateList && res.certificateList.length) {
    certificateFormList.value = res.certificateList.map(c => ({
      certName: c.name || '',
      certNo: c.certificateNo || '',
      certOrg: c.issuer || '',
      issueDate: formatDate(c.issueDate),
      expireDate: formatDate(c.expiryDate),
      source: c.source || '平台录入',
      status: c.status || '待认证',
      file: null,
      certificateImageUrl: c.certificateImageUrl || ''
    }))
  } else {
    certificateFormList.value = [{ certName: '', certNo: '', certOrg: '', issueDate: '', expireDate: '', source: '平台录入', status: '待认证', file: null, certificateImageUrl: '' }]
  }
  // 求职记录
  if (res.jobApplicationList && res.jobApplicationList.length) {
    jobApplicationFormList.value = res.jobApplicationList.map(j => ({
      applyCompany: j.company || '',
      applyPosition: j.position || '',
      applyDate: formatDate(j.applyDate),
      applyStatus: j.status || '已投递'
    }))
  } else {
    jobApplicationFormList.value = [{ applyCompany: '', applyPosition: '', applyDate: '', applyStatus: '已投递' }]
  }
  // 工作履历
  if (res.employmentList && res.employmentList.length) {
    employmentFormList.value = res.employmentList.map(e => ({
      company: e.company || '',
      position: e.position || '',
      startTime: formatDate(e.startDate),
      endTime: formatDate(e.endDate),
      salary: e.salary ? String(e.salary) : ''
    }))
  } else {
    employmentFormList.value = [{ company: '', position: '', startTime: '', endTime: '', salary: '' }]
  }
}

// 监听弹窗和 talent 变化，编辑时拉详情
watch([visible, () => props.talent], async ([v, t]) => {
  if (v && t && t.id) {
    await fetchTalentDetailAndFill(t.id)
    loadTalentSourceOptions() // 编辑时也确保人才来源有数据
  }
})

// 身份证号变化自动填充出生日期
watch(() => createForm.identityId, (val) => {
  if (val && val.length === 18) {
    const birth = val.substring(6, 14)
    if (/^\d{8}$/.test(birth)) {
      const year = birth.substring(0, 4)
      const month = birth.substring(4, 6)
      const day = birth.substring(6, 8)
      createForm.birthday = `${year}-${month}-${day}`
    }
  }
})

// 平台自营标签自动联动
watch(() => createForm.isSelfSupport, (val) => {
  const tag = '平台自营'
  if (val) {
    if (!createForm.tags.includes(tag)) {
      createForm.tags.push(tag)
    }
  } else {
    const idx = createForm.tags.indexOf(tag)
    if (idx !== -1) {
      createForm.tags.splice(idx, 1)
    }
  }
})
</script>

<style scoped>
.talent-create-step {
  margin-bottom: 24px;
}
.talent-create-form {
  margin-top: 10px;
}
.section-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 16px;
}
.talent-form-grid .el-form-item {
  margin-bottom: 18px;
}
.talent-tags-list {
  margin-top: 6px;
}
.recommend-title {
  font-weight: bold;
  margin-bottom: 6px;
  margin-top: 10px;
}
.recommend-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px 8px;
}
.recommend-tag {
  cursor: pointer;
  margin-bottom: 6px;
  user-select: none;
}
.recommend-tag:hover {
  background: #f0f9eb;
  color: #67c23a;
}
.talent-step-item.clickable {
  cursor: pointer;
}
.talent-step-item.active .el-step__title {
  color: #409eff !important;
}
</style>
