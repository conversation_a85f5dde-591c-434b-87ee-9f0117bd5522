<!--
  页面名称：预约场地抽屉
  功能描述：填写活动信息，直接预约场地，展示场地信息和已预约信息，支持表单校验、提交、取消，样式自适应
-->
<template>
  <el-drawer
    v-model="visible"
    :title="`直接预约场地 - ${siteInfo.name || ''}`"
    size="700px"
    direction="rtl"
    :with-header="true"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="onCancel"
    class="site-appointment-drawer"
  >
    <!-- 场地信息卡片 -->
    <el-card class="site-info-card" shadow="never">
      <div class="site-title">{{ siteInfo.name }}</div>
      <el-row :gutter="16" class="site-info-row">
        <el-col :sm="6" :xs="12">校区：{{ siteInfo.campus }}</el-col>
        <el-col :sm="6" :xs="12">类型：{{ siteInfo.type }}</el-col>
        <el-col :sm="6" :xs="12">容量：{{ siteInfo.seat }}座</el-col>
        <el-col :sm="6" :xs="12">位置：{{ siteInfo.location }}</el-col>
      </el-row>
      <el-row :gutter="16" class="site-info-row">
        <el-col :sm="18" :xs="24">设备：{{ siteInfo.equipment }}</el-col>
        <el-col :sm="6" :xs="24">
          状态：<el-tag type="success" v-if="siteInfo.status==='可用'">可用</el-tag>
          <el-tag type="warning" v-else-if="siteInfo.status==='已预约'">已预约</el-tag>
          <el-tag type="info" v-else-if="siteInfo.status==='维护中'">维护中</el-tag>
          <el-tag type="danger" v-else-if="siteInfo.status==='停用'">停用</el-tag>
        </el-col>
      </el-row>
    </el-card>
    <!-- 绿色提示 -->
    <el-alert type="success" show-icon :closable="false" class="mb-2" style="margin-top: 12px;">
      直接预约：填写信息后点击“立即预约”即可直接预约成功，无需等待确认。
    </el-alert>
    <!-- 预约表单 -->
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="110px"
      label-position="top"
      class="site-appointment-form"
    >
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="活动名称" prop="activityName" required>
            <el-input v-model="form.activityName" placeholder="请输入活动名称" maxlength="30" />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="24">
          <el-form-item label="活动类型" prop="activityType" required>
            <el-select v-model="form.activityType" placeholder="请选择活动类型">
              <el-option v-for="item in activityTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="开始日期" prop="startDate" required>
            <el-date-picker v-model="form.startDate" type="date" value-format="YYYY-MM-DD" placeholder="请选择开始日期" style="width:100%" />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="24">
          <el-form-item label="结束日期" prop="endDate" required>
            <el-date-picker v-model="form.endDate" type="date" value-format="YYYY-MM-DD" placeholder="请选择结束日期" style="width:100%" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="开始时间" prop="startTime" required>
            <el-time-picker v-model="form.startTime" placeholder="请选择开始时间" style="width:100%" />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="24">
          <el-form-item label="结束时间" prop="endTime" required>
            <el-time-picker v-model="form.endTime" placeholder="请选择结束时间" style="width:100%" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="预计人数" prop="peopleCount" required>
            <el-input v-model="form.peopleCount" placeholder="请输入预计人数" maxlength="5" />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="24">
          <el-form-item label="联系人" prop="contactName" required>
            <el-input v-model="form.contactName" placeholder="请输入联系人姓名" maxlength="10" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="联系电话" prop="contactPhone" required>
            <el-input v-model="form.contactPhone" placeholder="请输入联系电话" maxlength="20" />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="24">
          <el-form-item label="预约状态" prop="status">
            <el-select v-model="form.status" disabled>
              <el-option label="已确认" value="已确认" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" type="textarea" :rows="2" placeholder="请输入备注信息（可选）" maxlength="100" />
      </el-form-item>
    </el-form>
    <!-- 已有预约信息 -->
    <div v-if="reservedList.length" class="reserved-list">
      <div class="reserved-title">该场地已有预约</div>
      <el-card v-for="(item, idx) in reservedList" :key="idx" class="reserved-card" shadow="never">
        <div class="reserved-activity">{{ item.activityName }}</div>
        <div class="reserved-info">时间：{{ item.date }} {{ item.time }}</div>
        <div class="reserved-info">类型：{{ item.type }} | 人数：{{ item.peopleCount }} | 负责人：{{ item.contactName }}（{{ item.contactPhone }}）</div>
        <div class="reserved-info">备注：{{ item.remark || '无' }}</div>
        <el-tag type="success" class="reserved-status">已确认</el-tag>
      </el-card>
    </div>
    <!-- 底部按钮 -->
    <div style="text-align:right;margin-top:16px;">
      <el-button @click="onCancel">取消</el-button>
      <el-button type="primary" @click="onSubmit">立即预约</el-button>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch } from 'vue'

interface ReservedItem {
  activityName: string;
  date: string;
  time: string;
  type: string;
  peopleCount: string | number;
  contactName: string;
  contactPhone: string;
  remark?: string;
}

const props = defineProps({
  visible: { type: Boolean, default: false },
  siteInfo: { type: Object, default: () => ({}) },
  reservedList: { type: Array as () => ReservedItem[], default: () => [] }
})
const emit = defineEmits(['close', 'submit'])

const visible = ref(props.visible)
watch(() => props.visible, v => visible.value = v)
watch(visible, v => { if (!v) emit('close') })

const formRef = ref()
const form = ref({
  activityName: '',
  activityType: '',
  startDate: '',
  endDate: '',
  startTime: '',
  endTime: '',
  peopleCount: '',
  contactName: '',
  contactPhone: '',
  status: '已确认',
  remark: ''
})

const activityTypeOptions = [
  { label: '培训', value: '培训' },
  { label: '考试', value: '考试' },
  { label: '会议', value: '会议' },
  { label: '讲座', value: '讲座' },
  { label: '其他', value: '其他' }
]

const rules = {
  activityName: [{ required: true, message: '请输入活动名称', trigger: 'blur' }],
  activityType: [{ required: true, message: '请选择活动类型', trigger: 'change' }],
  startDate: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
  endDate: [{ required: true, message: '请选择结束日期', trigger: 'change' }],
  startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  endTime: [{ required: true, message: '请选择结束时间', trigger: 'change' }],
  peopleCount: [{ required: true, message: '请输入预计人数', trigger: 'blur' }],
  contactName: [{ required: true, message: '请输入联系人姓名', trigger: 'blur' }],
  contactPhone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }]
}

const onCancel = () => {
  emit('close')
}
const onSubmit = () => {
  formRef.value.validate((valid: boolean) => {
    if (!valid) return
    emit('submit', { ...form.value })
  })
}
</script>

<style scoped lang="scss">
.site-appointment-drawer {
  .el-drawer__header { font-size: 20px; font-weight: bold; }
  .el-drawer__body { max-height: 80vh; overflow-y: auto; }
  .site-info-card { margin-bottom: 8px; }
  .site-title { font-size: 18px; font-weight: bold; margin-bottom: 4px; }
  .site-info-row { font-size: 14px; color: #333; margin-bottom: 2px; }
  .reserved-list { margin-top: 18px; }
  .reserved-title { font-weight: bold; margin-bottom: 8px; }
  .reserved-card { margin-bottom: 8px; }
  .reserved-activity { font-weight: bold; font-size: 15px; margin-bottom: 2px; }
  .reserved-info { color: #555; font-size: 13px; margin-bottom: 2px; }
  .reserved-status { float: right; margin-top: -24px; }
}
@media (max-width: 800px) {
  .site-appointment-drawer .el-drawer { width: 98vw !important; min-width: 0; }
  .el-drawer__header { font-size: 16px; }
}
</style>
